import pandas as pd
import math
import numpy as np
import logging
import traceback
from datetime import date


class Correction:
    """
    Classe responsable de calculer les notes après corrections les entités
    à différents niveaux hiérarchiques (asset, subsidiary, cluster, division, group)
    en fonction de règles de pondération.

    Attributs:
        privacy (str): ('public', 'private').
        tc (object): Instance contenant les données et méthodes nécessaires au calcul.
        ip_correction (pd.DataFrame): Résultat final des corrections IP.
        category_correction (pd.DataFrame): Corrections par catégorie.
        family_correction (pd.DataFrame): Corrections par famille.
        saved_ip_cor (pd.DataFrame): Données des entités retirées, stockées pour concaténation ultérieure.
    """
    def __init__(self, tc_class):
        """
        Initialise la classe Correction avec une instance contenant les données de travail.

        Args:
            tc_class (object): Objet contenant les données et paramètres nécessaires.
        """
        self.privacy = tc_class.privacy
        self.tc = tc_class
        self.ip_correction = None
        self.category_correction = None
        self.family_correction = None
        self.saved_ip_cor = None
        # Initialize logger for this class
        self.my_logger = logging.getLogger("CBR-CORRECTION")

    @staticmethod
    def up_correction_formula(ps_ent, diff_after_cor, ps_sum_up_ent):
        """
        Calcule la note après correction au niveau supérieur basé sur un logarithme pondéré.

        Args:
            ps_ent (float): Taille de parc de l'entité.
            diff_after_cor (float): Différence de rating après correction.
            ps_sum_up_ent (float): Somme des tailles des entités supérieures.

        Returns:
            float: Correction calculée.
        """
        out = (math.log(ps_ent + 1) / ps_sum_up_ent) * diff_after_cor
        if out > 0:
            return out
        else:
            return 0

    @staticmethod
    def cat_correction_formula(xs, pond_sum, note_i, pond_i, size_parc, size_group, healthy_ip_th):
        """
        Applique la formule du calcule des notes des catégories après correction d'une IP,
        la logique du calcule est basé sur la formule inverse du calcule des notes des catégories.

        Args:
            xs (float): Moyenne pondérée des notes d'IP.
            pond_sum (float): Somme des poids.
            note_i (float): Note de l'IP à exclure temporairement.
            pond_i (float): Poids de l'IP à exclure.
            size_parc (int): Taille totale du parc.
            size_group (int): Taille du groupe.
            healthy_ip_th (float): Seuil de note pour une IP "saine".

        Returns:
            float: Nouvelle note de catégorie calculée.
    """
        xsn = (xs * pond_sum - note_i * pond_i) / (pond_sum - pond_i)

        num = xsn * math.log(size_group + 1) + healthy_ip_th * math.log(size_parc - size_group + 1)
        den = math.log(size_group + 1) + math.log(size_parc - size_group + 1)

        category_rating = num / den
        return category_rating

    @staticmethod
    def calculate_all_gain_entities(construct_class, df, level, rt_correction=False):
        """
        Calcule le gain pour chaque entité d'un niveau donné suite à une correction.

        Args:
            construct_class (object): Objet possédant une méthode log_wa.
            df (pd.DataFrame): Données contenant les colonnes de correction.
            level (str): Niveau d'entité (e.g., 'group', 'division').

        Returns:
            float: Gain calculé (>= 0).
        """
        if rt_correction:
            out = construct_class.log_wa(df[f"cat_{level}_rating_if_corr"], df[f"{level}_parc_size"])
        else:
            out = construct_class.log_wa(df[f"{level}_rating_if_corr"], df[f"{level}_parc_size"])
        if out > 0:
            return out
        else:
            return 0

    def true_rating_category(self, level, entity, cat):
        """
        Récupère la note réelle d'une catégorie pour une entité donnée à un certain niveau.

        Args:
            level (str): Niveau d'agrégation.
            entity (str): Nom de l'entité.
            cat (str): Catégorie d'IP.

        Returns:
            float: Note réelle de la catégorie.
        """
        try:
            # Defensive check: ensure data_category exists and has required columns
            if not hasattr(self.tc, 'data_category') or self.tc.data_category is None or self.tc.data_category.empty:
                self.my_logger.warning(f"data_category is empty, returning default rating for {level}/{entity}/{cat}")
                return 0.0

            required_cols = ["level", level, "category", "category_rating"]
            missing_cols = [col for col in required_cols if col not in self.tc.data_category.columns]
            if missing_cols:
                self.my_logger.warning(f"Missing columns in data_category: {missing_cols}, returning default rating")
                return 0.0

            filtered_data = self.tc.data_category[
                (self.tc.data_category["level"] == level)
                & (self.tc.data_category[level] == entity)
                & (self.tc.data_category["category"] == cat)
            ]

            if filtered_data.empty:
                self.my_logger.warning(f"No data found for {level}/{entity}/{cat}, returning default rating")
                return 0.0

            true_rating = filtered_data.category_rating.values[0]
            return true_rating

        except Exception as e:
            self.my_logger.error(f"Error in true_rating_category for {level}/{entity}/{cat}: {e}")
            return 0.0

    def save_remove_entity(self, df):
        """
        Enregistre les entités supprimées pour les réintégrer ultérieurement.

        Args:
            df (pd.DataFrame): Données des entités retirées.
        """
        if self.saved_ip_cor is not None:
            self.saved_ip_cor = pd.concat([self.saved_ip_cor, df])
        else:
            self.saved_ip_cor = df

    def create_first_ip_correction_level(self, entities_level):
        """
        Applique le calcule des notes après correction au niveau le plus bas (asset ou subsidiary).

        Args:
            entities_level (str): "asset" ou "entities".

        Returns:
            pd.DataFrame: Données corrigées avec notes ajustées par IP et catégorie.
        """
        if entities_level == "entities":
            # Defensive check: ensure data_ip exists and has subsidiary column
            if hasattr(self.tc, 'data_ip') and self.tc.data_ip is not None and not self.tc.data_ip.empty:
                if 'subsidiary' in self.tc.data_ip.columns:
                    issued_entities = self.tc.data_ip.subsidiary.dropna().unique()
                else:
                    self.my_logger.warning("subsidiary column missing in data_ip, using empty array")
                    issued_entities = np.array([])
            else:
                self.my_logger.warning("data_ip is None or empty, using empty array")
                issued_entities = np.array([])
            entities_list = self.tc.list_level
            df_ip = getattr(self.tc, 'data_ip', pd.DataFrame())
            level = "subsidiary"
        else:
            # Defensive check: ensure data_ip_asset exists and has asset column
            if hasattr(self.tc, 'data_ip_asset') and self.tc.data_ip_asset is not None and not self.tc.data_ip_asset.empty:
                if 'asset' in self.tc.data_ip_asset.columns:
                    issued_entities = self.tc.data_ip_asset.asset.dropna().unique()
                else:
                    self.my_logger.warning("asset column missing in data_ip_asset, using empty array")
                    issued_entities = np.array([])
            else:
                self.my_logger.warning("data_ip_asset is None or empty, using empty array")
                issued_entities = np.array([])
            entities_list = ["asset"]
            df_ip = getattr(self.tc, 'data_ip_asset', pd.DataFrame())
            level = "asset"

        data = []
        for entity in issued_entities:
            df_entity = df_ip[df_ip[level] == entity]
            for cat in df_entity["category"].unique():
                df_cat = df_entity[df_entity["category"] == cat]
                true_rating = self.true_rating_category(level, entity, cat)

                if df_cat["ip_id"].nunique() == 1:
                    df_cat = df_cat[entities_list + ["family", "category", "ip_id"]]
                    df_cat["full_category_rating"] = true_rating
                    df_cat["category_rating"] = self.tc.healthy_ip_th
                    df_cat[f"cat_{level}_rating_if_corr"] = self.tc.healthy_ip_th - true_rating
                    data.append(df_cat.values.tolist()[0])

                else:
                    somme_pond = sum(df_cat["ip_rating_weight"])
                    df_cat.loc[:, "issued_ips"] -= 1
                    gb = entities_list + ["family", "category", "parc_size"]
                    rating = df_cat.groupby(gb).apply(lambda x:
                                                      self.tc.wa(x["ip_rating"],
                                                                 x["ip_rating_weight"])).values[0]

                    category_correction_rating = df_cat.apply(
                        lambda row, rating=rating, somme_pond=somme_pond: pd.Series({
                            "category_rating": self.cat_correction_formula(rating,
                                                                           somme_pond,
                                                                           row["ip_rating"],
                                                                           row["ip_rating_weight"],
                                                                           row["parc_size"],
                                                                           row["issued_ips"],
                                                                           self.tc.healthy_ip_th)}), axis=1)

                    df_cat = df_cat.assign(category_rating=category_correction_rating)
                    column_list = entities_list + ["family", "category", "ip_id", "category_rating"]
                    df_cat = df_cat[column_list]
                    df_cat["full_category_rating"] = true_rating
                    df_cat[f"cat_{level}_rating_if_corr"] = df_cat["category_rating"] - true_rating
                    df_cat[f"cat_{level}_rating_if_corr"] = df_cat[f"cat_{level}_rating_if_corr"].apply(lambda x: max(x, 0))

                    data += [row for row in df_cat.values.tolist()]

        df = pd.DataFrame(data, columns=entities_list + ["family",
                                                         "category",
                                                         "ip_id",
                                                         "category_rating",
                                                         "full_category_rating",
                                                         f"cat_{level}_rating_if_corr"])
        return df

    def calculate_up_entity_ip_correction(self, level, df):
        """
        Calcule la propagation des notes après corrections vers les niveaux supérieurs.

        Args:
            level (str): Niveau cible de propagation du calcule après correction.
            df (pd.DataFrame): Données d'entrée avec corrections de niveau inférieur.

        Returns:
            pd.DataFrame: Résultat des corrections IP pour le niveau donné.
        """
        list_all_levels = self.tc.list_level
        if level != "group":
            list_merge = (
                list_all_levels[: list_all_levels.index(level) + 1] + ["category", "family"]
            )
            level_inf = list_all_levels[list_all_levels.index(level) + 1]
        else:
            list_merge = ["category", "family"]
            level_inf = "division"

        if not df.empty:
            df_save = df[df["remove_" + level_inf]]
            self.save_remove_entity(df_save)
            df = df[~df["remove_" + level_inf]]
            data_cat = self.tc.data_category[self.tc.data_category["level"] == level]

            # Defensive check: ensure parc_size_sum column exists
            merge_cols = list_merge + ["category_rating"]
            if "parc_size_sum" in data_cat.columns:
                merge_cols.append("parc_size_sum")
            else:
                self.my_logger.warning("parc_size_sum column missing in data_cat, using parc_size instead")
                if "parc_size" in data_cat.columns:
                    merge_cols.append("parc_size")
                    # Create parc_size_sum from parc_size if missing
                    data_cat = data_cat.copy()
                    data_cat["parc_size_sum"] = data_cat["parc_size"]
                else:
                    self.my_logger.warning("Neither parc_size_sum nor parc_size available, creating default")
                    data_cat = data_cat.copy()
                    data_cat["parc_size_sum"] = 1.0
                    merge_cols.append("parc_size_sum")

            # Use safe merge to prevent data type mismatch errors
            from .utils.utils import safe_merge_with_type_conversion
            merge_on_level = safe_merge_with_type_conversion(
                df,
                data_cat[merge_cols],
                on=list_merge,
                how="inner")

            data_cat_inf = self.tc.data_category[self.tc.data_category["level"] == level_inf]

            ip_correction = safe_merge_with_type_conversion(
                merge_on_level,
                data_cat_inf[list_merge + [level_inf, "parc_size"]],
                on=list_merge + [level_inf],
                how="inner")

            ip_correction[f"cat_{level}_rating_if_corr"] = ip_correction.apply(
                lambda row: self.up_correction_formula(row["parc_size"],
                                                       row[f"cat_{level_inf}_rating_if_corr"],
                                                       row["parc_size_sum"]), axis=1)

            ip_correction.rename(columns={"category_rating_y": "category_rating"}, inplace=True)
            ip_correction = ip_correction.drop(["category_rating_x",
                                                "parc_size_sum",
                                                "parc_size"], axis=1)
        else:
            ip_correction = pd.DataFrame(columns=list_all_levels + ["family",
                                                                    "category",
                                                                    "ip_id",
                                                                    "full_category_rating",
                                                                    "category_rating",
                                                                    "cat_subsidiary_rating_if_corr",
                                                                    "cat_cluster_rating_if_corr",
                                                                    "cat_division_rating_if_corr",
                                                                    "cat_group_rating_if_corr"])
        ip_correction = self.tc.remove_entity(ip_correction, level)
        return ip_correction

    def create_ip_correction(self):
        """
        Orchestre l'ensemble du processus de calcule après correction des IP, de l'asset jusqu'au group,
        et assemble les résultats finaux.

        Returns:
            pd.DataFrame: DataFrame complète avec les corrections IP appliquées.
        """
        try:
            self.my_logger.info("Starting create_ip_correction")

            self.my_logger.info("Creating asset correction...")
            asset_correction = self.create_first_ip_correction_level("asset")
            self.my_logger.info(f"Asset correction created with shape: {asset_correction.shape if hasattr(asset_correction, 'shape') else 'No shape'}")

            self.my_logger.info("Creating subsidiary correction...")
            sub_correction = self.create_first_ip_correction_level("entities")
            self.my_logger.info(f"Subsidiary correction created with shape: {sub_correction.shape if hasattr(sub_correction, 'shape') else 'No shape'}")

            self.my_logger.info("Removing entity from subsidiary correction...")
            sub_correction = self.tc.remove_entity(sub_correction, "subsidiary")
            self.my_logger.info("Entity removed from subsidiary correction")
            
            self.my_logger.info("Calculating cluster correction...")
            clust_correction = self.calculate_up_entity_ip_correction("cluster", sub_correction)
            self.my_logger.info(f"Cluster correction calculated with shape: {clust_correction.shape if hasattr(clust_correction, 'shape') else 'No shape'}")

            self.my_logger.info("Calculating division correction...")
            div_correction = self.calculate_up_entity_ip_correction("division", clust_correction)
            self.my_logger.info(f"Division correction calculated with shape: {div_correction.shape if hasattr(div_correction, 'shape') else 'No shape'}")

            self.my_logger.info("Calculating group correction...")
            entities_correction = self.calculate_up_entity_ip_correction("group", div_correction)
            self.my_logger.info(f"Group correction calculated with shape: {entities_correction.shape if hasattr(entities_correction, 'shape') else 'No shape'}")

            if self.saved_ip_cor is not None:
                self.my_logger.info("Concatenating with saved IP correction...")
                entities_correction = pd.concat([entities_correction, self.saved_ip_cor])
                self.my_logger.info("Saved IP correction concatenated")

            self.my_logger.info("Checking if 'level' column exists in entities_correction...")
            if "level" in entities_correction.columns:
                self.my_logger.warning("'level' column already exists in entities_correction, dropping it first")
                entities_correction = entities_correction.drop("level", axis=1)

            self.my_logger.info("Inserting 'level' column in entities_correction...")
            entities_correction.insert(1, "level", "subsidiary")
            self.my_logger.info("'level' column inserted in entities_correction")

            self.my_logger.info("Checking if 'level' column exists in asset_correction...")
            if "level" in asset_correction.columns:
                self.my_logger.warning("'level' column already exists in asset_correction, dropping it first")
                asset_correction = asset_correction.drop("level", axis=1)

            self.my_logger.info("Inserting 'level' column in asset_correction...")
            asset_correction.insert(1, "level", "asset")
            self.my_logger.info("'level' column inserted in asset_correction")

            # Permet de gérer les cas de DF vides et évite les warnings
            # TODO : A fonctionnaliser
            self.my_logger.info("Preparing to concatenate asset and entities corrections...")
            df_list = [asset_correction, entities_correction]
            all_columns = pd.Index([])
            for df1 in df_list:
                all_columns = all_columns.union(df1.columns)
            df_non_empty = [df2 for df2 in df_list if not df2.isna().all().all()]
            if df_non_empty:
                self.my_logger.info("Concatenating non-empty DataFrames...")
                ip_correction = pd.concat(df_non_empty, ignore_index=True)
                ip_correction = ip_correction.reindex(columns=all_columns)
                self.my_logger.info(f"IP correction concatenated with shape: {ip_correction.shape}")
            else:
                self.my_logger.warning("All DataFrames are empty, creating empty DataFrame")
                ip_correction = pd.DataFrame()
                ip_correction = ip_correction.reindex(columns=all_columns)

            self.my_logger.info("Getting parc sizes...")
            ip_correction = self.tc.get_parc_size("asset", ip_correction)
            ip_correction.rename(columns={"parc_size": "asset_parc_size"}, inplace=True)
            self.my_logger.info("Asset parc size calculated")

            ip_correction = self.tc.get_parc_size("subsidiary", ip_correction)
            ip_correction.rename(columns={"parc_size": "subsidiary_parc_size"}, inplace=True)
            self.my_logger.info("Subsidiary parc size calculated")

            ip_correction = self.tc.get_parc_size("cluster", ip_correction)
            ip_correction["subsidiary"] = ip_correction["subsidiary"].astype(float).fillna(ip_correction["subsidiary_x"].astype(float))
            ip_correction.rename(columns={"parc_size": "cluster_parc_size"}, inplace=True)
            self.my_logger.info("Cluster parc size calculated")

            ip_correction = self.tc.get_parc_size("division", ip_correction)
            ip_correction.rename(columns={"parc_size": "division_parc_size"}, inplace=True)
            self.my_logger.info("Division parc size calculated")

            ip_correction = self.tc.get_parc_size("group", ip_correction)
            ip_correction.rename(columns={"parc_size": "group_parc_size"}, inplace=True)
            self.my_logger.info("Group parc size calculated")

            self.ip_correction = ip_correction

            # ip_correction.drop("parc_size", axis=1, inplace=True)
            # ip_correction.drop("issued_ips", axis=1, inplace=True)
            # ip_correction.drop("parc_size_sum", axis=1, inplace=True)

            self.my_logger.info("Dropping unnecessary columns...")
            ip_correction.drop("full_category_rating", axis=1, inplace=True)
            ip_correction.drop("remove_subsidiary", axis=1, inplace=True, errors="ignore")
            ip_correction.drop("remove_cluster", axis=1, inplace=True, errors="ignore")
            ip_correction.drop("remove_division", axis=1, inplace=True, errors="ignore")
            self.my_logger.info("Unnecessary columns dropped")

            self.my_logger.info("Inserting timestamp...")
            ip_correction.insert(0, "timestamp", date.today())
            self.my_logger.info("Timestamp inserted")

            # ip_correction.fillna({"cat_subsidiary_rating_if_corr": 0,
            #                       "cat_cluster_rating_if_corr": 0,
            #                       "cat_division_rating_if_corr": 0}, inplace=True)
            self.my_logger.info("Assigning group and privacy...")
            ip_correction = ip_correction.assign(group="Orange")
            ip_correction = ip_correction.assign(privacy=self.privacy)
            self.my_logger.info("Group and privacy assigned")

            self.my_logger.info("create_ip_correction completed successfully")
            return ip_correction

        except Exception as e:
            self.my_logger.error(f"Error in create_ip_correction: {str(e)}")
            self.my_logger.error(f"Exception type: {type(e).__name__}")
            self.my_logger.error(f"Full traceback: {traceback.format_exc()}")
            # Return empty DataFrame in case of error
            return pd.DataFrame()

    def get_rating(self, df, level, entity):
        """
        Récupère la note totale d'une entité à un niveau donné depuis un DataFrame.

        Args:
            df (pd.DataFrame): Données avec colonnes de rating.
            level (str): Niveau de l'entité.
            entity (str): Nom de l'entité.

        Returns:
            float: Note de l'entité ou 0.0 si non trouvée.
        """
        # Defensive check: handle empty DataFrame
        if df is None or df.empty:
            self.my_logger.warning(f"DataFrame is empty when getting rating for {level}/{entity}")
            return 0.0

        # Defensive check: ensure required columns exist
        if 'total_rating' not in df.columns:
            self.my_logger.warning(f"total_rating column missing when getting rating for {level}/{entity}")
            return 0.0

        try:
            if level == "group":
                filtered_df = df[df["level"] == "group"]
            elif level == "asset":
                if 'asset' not in df.columns:
                    self.my_logger.warning(f"asset column missing when getting rating for {level}/{entity}")
                    return 0.0
                filtered_df = df[df["asset"] == entity]
            else:
                if level not in df.columns:
                    self.my_logger.warning(f"{level} column missing when getting rating for {level}/{entity}")
                    return 0.0
                filtered_df = df[(df["level"] == level) & (df[level] == entity)]

            # Defensive check: handle empty filtered result
            if filtered_df.empty:
                self.my_logger.warning(f"No data found for {level}/{entity}")
                return 0.0

            rating = filtered_df.total_rating.values[0]
            return rating

        except Exception as e:
            self.my_logger.error(f"Error getting rating for {level}/{entity}: {e}")
            return 0.0

    def asset_plugin_correction(self, df, row, plugin_level):
        """
        Simule une correction d'un niveau de plugin (par "category" ou "family") sur un asset et retourne le gain de rating.

        Args:
            df (pd.DataFrame): Données initiales.
            row (pd.Series): Ligne représentant un niveau de plugin pour un asset.
            plugin_level (str): Niveau du plugin à corriger ("category" ou "family").

        Returns:
            float: Différence de rating suite à la correction.
        """
        temp = df.copy()
        asset = row.asset
        plugin = row[plugin_level]
        temp.loc[(temp["asset"] == asset) & (temp[plugin_level] == plugin), "category_rating"] = self.tc.healthy_ip_th

        if plugin_level == "category":
            temp = self.tc.calculate_up_plugin_level("family", temp, "asset")

        asset_total_corrected = self.tc.calculate_up_plugin_level("total", temp, "asset")

        rating = self.tc.data_total[self.tc.data_total["asset"] == asset].total_rating.values[0]
        diff_rating = self.get_rating(asset_total_corrected, "asset", asset) - rating
        return diff_rating

    def sub_plugin_correction(self, df, row, plugin_level):
        """
        Applique une correction d'un niveau de plugin sur une IP d'une entité et calcule
        l'impact sur les niveaux hiérarchiques supérieurs.

        Args:
            df (pd.DataFrame): Données initiales.
            row (pd.Series): Ligne représentant un niveau de plugin pour un subsidary.
            plugin_level (str): Niveau du plugin à corriger ("category", "family").

        Returns:
            tuple: Gains de rating pour subsidiary, cluster, division, group.
        """
        # Defensive check: handle empty DataFrame or None row
        if df is None or df.empty or row is None:
            self.my_logger.warning("Empty DataFrame or None row in sub_plugin_correction")
            return 0.0, 0.0, 0.0, 0.0

        # Defensive check: ensure required columns exist in row
        required_cols = ['division', 'cluster', 'subsidiary', plugin_level]
        missing_cols = [col for col in required_cols if col not in row.index]
        if missing_cols:
            self.my_logger.warning(f"Missing required columns in row: {missing_cols}")
            return 0.0, 0.0, 0.0, 0.0

        try:
            temp = df.copy()
            division = row.division
            cluster = row.cluster
            subsidiary = row.subsidiary
            plugin = row[plugin_level]
            plugin_rating = plugin_level + "_rating"

            # Defensive check: handle None or NaN values
            if pd.isna(division) or pd.isna(cluster) or pd.isna(subsidiary):
                self.my_logger.warning(f"NaN values found in row: division={division}, cluster={cluster}, subsidiary={subsidiary}")
                return 0.0, 0.0, 0.0, 0.0

            # Defensive check: ensure required columns exist in DataFrame
            if plugin_level not in temp.columns or plugin_rating not in temp.columns:
                self.my_logger.warning(f"Missing required columns in DataFrame: {plugin_level} or {plugin_rating}")
                return 0.0, 0.0, 0.0, 0.0

            temp.loc[(temp["subsidiary"] == subsidiary) & (temp[plugin_level] == plugin), plugin_rating] = self.tc.healthy_ip_th

            if plugin_level == "category":
                temp = self.tc.calculate_up_plugin_level("family", temp, "subsidiary")

            sub_corrected = self.tc.calculate_up_plugin_level("total", temp, "subsidiary")
            sub_corrected = self.tc.remove_entity(sub_corrected, "subsidiary")
            clust_corrected = self.tc.calculate_up_entity("cluster", "total", sub_corrected)
            div_corrected = self.tc.calculate_up_entity("division", "total", clust_corrected)
            group_corrected = self.tc.calculate_up_entity("group", "total", div_corrected)

            sub_rating = (self.get_rating(sub_corrected, "subsidiary", subsidiary)
                          - self.get_rating(self.tc.data_total, "subsidiary", subsidiary))

        except Exception as e:
            self.my_logger.error(f"Error in sub_plugin_correction calculation: {e}")
            return 0.0, 0.0, 0.0, 0.0

        # Defensive check: ensure tc attributes exist
        if not hasattr(self.tc, 'remove_subs') or not hasattr(self.tc, 'remove_clust') or not hasattr(self.tc, 'remove_div'):
            self.my_logger.warning("Missing remove_* attributes in tc, using default calculations")
            try:
                clust_rating = (self.get_rating(clust_corrected, "cluster", cluster)
                                - self.get_rating(self.tc.data_total, "cluster", cluster))
                div_rating = (self.get_rating(div_corrected, "division", division)
                              - self.get_rating(self.tc.data_total, "division", division))
                group_rating = (self.get_rating(group_corrected, "group", division)
                                - self.get_rating(self.tc.data_total, "group", division))
                return sub_rating, clust_rating, div_rating, group_rating
            except Exception as e:
                self.my_logger.error(f"Error in default rating calculations: {e}")
                return 0.0, 0.0, 0.0, 0.0

        try:
            # Defensive check: ensure remove lists exist and handle None values
            remove_subs = getattr(self.tc, 'remove_subs', [])
            remove_clust = getattr(self.tc, 'remove_clust', [])
            remove_div = getattr(self.tc, 'remove_div', [])

            if subsidiary in remove_subs:
                clust_rating = div_rating = group_rating = 0
                return sub_rating, clust_rating, div_rating, group_rating
            elif cluster in remove_clust:
                clust_rating = (self.get_rating(clust_corrected, "cluster", cluster)
                                - self.get_rating(self.tc.data_total, "cluster", cluster))
                div_rating = group_rating = 0
                return sub_rating, clust_rating, div_rating, group_rating
            elif division in remove_div:
                clust_rating = (self.get_rating(clust_corrected, "cluster", cluster)
                                - self.get_rating(self.tc.data_total, "cluster", cluster))
                div_rating = (self.get_rating(div_corrected, "division", division)
                              - self.get_rating(self.tc.data_total, "division", division))
                group_rating = 0
                return sub_rating, clust_rating, div_rating, group_rating
            else:
                clust_rating = (self.get_rating(clust_corrected, "cluster", cluster)
                                - self.get_rating(self.tc.data_total, "cluster", cluster))
                div_rating = (self.get_rating(div_corrected, "division", division)
                              - self.get_rating(self.tc.data_total, "division", division))

                group_rating = (self.get_rating(group_corrected, "group", division)
                                - self.get_rating(self.tc.data_total, "group", division))

            return sub_rating, clust_rating, div_rating, group_rating

        except Exception as e:
            self.my_logger.error(f"Error in rating calculations: {e}")
            return 0.0, 0.0, 0.0, 0.0

    def create_plugin_correction(self, construct_class, plugin_level, df_public, df_private, rt_correction=False):
        """
        Agrège et calcule des scores induits par la correction d'une category ou family appliqué
        aux données publiques et privées pour les niveaux `asset` et `subsidiary`.

        Cette méthode permet d'évaluer l'impact d'une category ou family sur les entités 'asset' et 'subsidiary',
        ainsi que sur les niveaux hiérarchiques supérieurs (cluster, division, group).
        Elle fusionne les données publiques et privées, puis effectue les agrégations
        nécessaires via des pondérations logarithmiques.

        Args:
            construct_class (object): Classe contenant les méthodes utilitaires comme `log_wa` pour le calcul pondéré logarithmique.
            plugin_level (str): Niveau du plugin (soit 'category' soit 'family').
            df_public (pd.DataFrame): Données publiques à intégrer dans le calcul.
            df_private (pd.DataFrame): Données privées à intégrer dans le calcul.

        Returns:
            pd.DataFrame: Un DataFrame contenant les colonnes suivantes (en fonction du niveau) :
                - `plugin_level_rating`: Score moyen pondéré après application du plugin.
                - `asset_rating_if_corr`: Gain de score pour le niveau `asset`.
                - `subsidiary_rating_if_corr`, `cluster_rating_if_corr`, `division_rating_if_corr`, `group_rating_if_corr`:
                Gains potentiels aux niveaux supérieurs si le plugin est appliqué à `subsidiary`.
        """
        # Defensive check: handle None or empty DataFrames
        if df_public is None:
            df_public = pd.DataFrame()
        if df_private is None:
            df_private = pd.DataFrame()

        # Both DataFrames are empty
        if df_public.empty and df_private.empty:
            self.my_logger.warning("Both public and private DataFrames are empty in create_plugin_correction")
            return pd.DataFrame()

        if rt_correction:
            cat_rating = "cat_"
        else:
            cat_rating = ""

        if plugin_level == "family":
            plugin_list = [plugin_level]
        elif plugin_level == "category":
            plugin_list = [plugin_level, "family"]
        else:
            plugin_list = []

        list_levels = ["subsidiary", "asset"]
        if rt_correction:
            common_cols_list = ["timestamp", "level"] + plugin_list + ["group", "ip_id"]
        else:
            common_cols_list = ["timestamp", "level"] + plugin_list + ["group"]
        df = pd.DataFrame()

        for level in list_levels:
            df_list = [df_public[df_public["level"] == level],
                       df_private[df_private["level"] == level]]
            # Identifier l'ensemble des colonnes présentes dans les DataFrames
            all_columns = pd.Index([])
            for df1 in df_list:
                all_columns = all_columns.union(df1.columns)
            df_non_empty = [df2 for df2 in df_list if not df2.isna().all().all()]
            if df_non_empty:
                temp = pd.concat(df_non_empty, ignore_index=True)
                temp = temp.reindex(columns=all_columns)
            else:
                temp = pd.DataFrame()
                temp = temp.reindex(columns=all_columns)

            if level == "asset":
                temp = temp[temp["asset_parc_size"] >= 1]
                group_cols = common_cols_list + ["asset"]
                gb = temp.groupby(group_cols).apply(lambda x: pd.Series(
                    {
                        plugin_level + "_rating": construct_class.log_wa(x[plugin_level + "_rating"],
                                                                         x["asset_parc_size"]),
                        f"{cat_rating}asset_rating_if_corr": self.calculate_all_gain_entities(construct_class, x, "asset", rt_correction)
                    }))
                # Suppression des colonnes dupliquées avant reset_index
                for col in group_cols:
                    if col in gb.columns:
                        gb = gb.drop(columns=[col])
                gb = gb.reset_index()
            else:
                temp = temp[temp["subsidiary_parc_size"] >= 1]
                group_cols = common_cols_list + construct_class.list_level
                gb = (
                    temp.groupby(group_cols)
                    .apply(
                        lambda x: pd.Series(
                            {
                                plugin_level + "_rating": construct_class.log_wa(x[plugin_level + "_rating"],
                                                                                 x["subsidiary_parc_size"]),
                                f"{cat_rating}subsidiary_rating_if_corr": self.calculate_all_gain_entities(construct_class, x,
                                                                                              "subsidiary", rt_correction),
                                f"{cat_rating}cluster_rating_if_corr": self.calculate_all_gain_entities(construct_class, x,
                                                                                           "cluster", rt_correction),
                                f"{cat_rating}division_rating_if_corr": self.calculate_all_gain_entities(construct_class, x,
                                                                                            "division", rt_correction),
                                f"{cat_rating}group_rating_if_corr": self.calculate_all_gain_entities(construct_class, x,
                                                                                         "group", rt_correction),
                            }
                        )
                    )
                )
                # Suppression des colonnes dupliquées avant reset_index
                for col in group_cols:
                    if col in gb.columns:
                        gb = gb.drop(columns=[col])
                gb = gb.reset_index()

            df = pd.concat([df, gb])
        # Defensive check: ensure cluster columns exist before applying corrections
        if "cluster" in df.columns:
            if f"{cat_rating}cluster_rating_if_corr" in df.columns:
                df[f"{cat_rating}cluster_rating_if_corr"] = np.where(df["cluster"] >= 1000, np.nan, df[f"{cat_rating}cluster_rating_if_corr"])
            df["cluster"] = np.where(df["cluster"] >= 1000, np.nan, df["cluster"])
        else:
            self.my_logger.warning("cluster column missing in create_plugin_correction, skipping cluster corrections")

        # df.drop("parc_size", axis=1, inplace=True)
        # df.drop("issued_ips", axis=1, inplace=True)
        # df.drop("parc_size_sum", axis=1, inplace=True)

        return df

    def create_privacy_plugin_correction(self, plugin_level):
        """
        Calcule les scores des entités en cas de correction d'un niveau de plugin ('category', 'family') par privacy.

        Cette méthode permet de simuler l'effet de la correction d'une 'category' ou 'family' en fonction de la privacy.
        Elle évalue l'impact de cette correction sur le score total des asset et sur les
        entités (subsidiary, cluster, division, group), en calculant les écarts de score avant/après correction.

        Args:
            plugin_level (str): Le niveau du plugin appliqué ('category' ou 'family').

        Returns:
            pd.DataFrame: Un DataFrame combiné des corrections appliquées pour le niveau 'asset' et 'subsidiary', incluant :
                - Les colonnes de score corrigé pour chaque niveau ('asset_rating_if_corr', 'subsidiary_rating_if_corr', etc.)
                - Les tailles de parc pour chaque niveau
                - Les colonnes spécifiques au plugin ('category_rating', 'family_rating')
                - Métadonnées
        """
        try:
            self.my_logger.info(f"Starting create_privacy_plugin_correction for plugin_level: {plugin_level}")

            if plugin_level == "category":
                self.my_logger.info("Using data_category for category plugin")
                df = self.tc.data_category
                plugin_list_rating = ["family", plugin_level, plugin_level + "_rating"]
            else:
                self.my_logger.info("Using data_family for family plugin")
                df = self.tc.data_family
                plugin_list_rating = ["family", plugin_level + "_rating"]

            self.my_logger.info("Processing asset level...")
            df_asset = df[df["level"] == "asset"]
            df_asset = df_asset.copy()
            df_asset.rename(columns={"parc_size": "asset_parc_size"}, inplace=True)
            self.my_logger.info(f"Asset DataFrame created with shape: {df_asset.shape}")

            if not df_asset.empty:
                self.my_logger.info("Calculating asset rating corrections...")
                df_asset["asset_rating_if_corr"] = df_asset.apply(lambda row:
                                                                  self.asset_plugin_correction(df_asset,
                                                                                               row,
                                                                                               plugin_level),
                                                                  axis=1, result_type="expand")
                self.my_logger.info("Asset rating corrections calculated")
            else:
                self.my_logger.warning("Asset DataFrame is empty, creating empty structure")
                df_asset = df_asset.assign(asset_rating_if_corr="")
                # Defensive check: ensure parc_size_sum column exists
                expected_cols = ["timestamp", "privacy", "level", "group", "division", "cluster",
                               "subsidiary", "asset", "asset_rating_if_corr", "asset_parc_size", "issued_ips"]
                if "parc_size_sum" in df_asset.columns:
                    expected_cols.append("parc_size_sum")
                else:
                    self.my_logger.warning("parc_size_sum missing in asset DataFrame, using asset_parc_size")
                    if "asset_parc_size" in df_asset.columns:
                        df_asset["parc_size_sum"] = df_asset["asset_parc_size"]
                        expected_cols.append("parc_size_sum")
                    else:
                        df_asset["parc_size_sum"] = 1.0
                        expected_cols.append("parc_size_sum")

                available_cols = [col for col in expected_cols + plugin_list_rating if col in df_asset.columns]
                df_asset = df_asset[available_cols]

            self.my_logger.info("Processing subsidiary level...")
            df_sub = df[df["level"] == "subsidiary"]
            df_sub = df_sub.copy()
            self.my_logger.info(f"Subsidiary DataFrame created with shape: {df_sub.shape}")

            if not df_sub.empty:
                self.my_logger.info("Calculating subsidiary rating corrections...")
                df_sub[["subsidiary_rating_if_corr",
                        "cluster_rating_if_corr",
                        "division_rating_if_corr",
                        "group_rating_if_corr"]] = df_sub.apply(
                    lambda row: self.sub_plugin_correction(df_sub, row, plugin_level),
                    axis=1,
                    result_type="expand"
                )
                self.my_logger.info("Subsidiary rating corrections calculated")

                self.my_logger.info("Renaming parc_size columns...")
                df_sub.rename(columns={"parc_size": "subsidiary_parc_size"}, inplace=True)
                self.my_logger.info("Getting cluster parc size...")
                df_sub = self.tc.get_parc_size("cluster", df_sub)
                df_sub["subsidiary"] = df_sub["subsidiary"].astype(float).fillna(df_sub["subsidiary_x"].astype(float))
                df_sub.rename(columns={"parc_size": "cluster_parc_size"}, inplace=True)
                self.my_logger.info("Getting division parc size...")
                df_sub = self.tc.get_parc_size("division", df_sub)
                df_sub.rename(columns={"parc_size": "division_parc_size"}, inplace=True)
                self.my_logger.info("Getting group parc size...")
                df_sub = self.tc.get_parc_size("group", df_sub)
                df_sub.rename(columns={"parc_size": "group_parc_size"}, inplace=True)
                self.my_logger.info("All parc sizes calculated for subsidiary")

            else:
                self.my_logger.warning("Subsidiary DataFrame is empty, creating empty structure")
                df_sub = df_sub.assign(subsidiary_rating_if_corr="")
                df_sub = df_sub.assign(cluster_rating_if_corr="")
                df_sub = df_sub.assign(division_rating_if_corr="")
                df_sub = df_sub.assign(group_rating_if_corr="")
                df_sub = df_sub.assign(subsidiary_parc_size="")
                df_sub = df_sub.assign(cluster_parc_size="")
                df_sub = df_sub.assign(division_parc_size="")
                df_sub = df_sub.assign(group_parc_size="")
                # Defensive check: ensure parc_size_sum column exists
                expected_cols = ["timestamp", "privacy", "level", "group", "division", "cluster",
                               "subsidiary", "asset", "subsidiary_rating_if_corr", "cluster_rating_if_corr",
                               "division_rating_if_corr", "group_rating_if_corr", "issued_ips",
                               "subsidiary_parc_size", "cluster_parc_size", "division_parc_size", "group_parc_size"]
                if "parc_size_sum" in df_sub.columns:
                    expected_cols.append("parc_size_sum")
                else:
                    self.my_logger.warning("parc_size_sum missing in subsidiary DataFrame, using subsidiary_parc_size")
                    if "subsidiary_parc_size" in df_sub.columns:
                        df_sub["parc_size_sum"] = df_sub["subsidiary_parc_size"]
                        expected_cols.append("parc_size_sum")
                    else:
                        df_sub["parc_size_sum"] = 1.0
                        expected_cols.append("parc_size_sum")

                available_cols = [col for col in expected_cols + plugin_list_rating if col in df_sub.columns]
                df_sub = df_sub[available_cols]

            self.my_logger.info("Concatenating asset and subsidiary DataFrames...")
            # df = pd.concat([df_asset, df_sub])
            df_list = [df_asset, df_sub]
            all_columns = pd.Index([])
            for df in df_list:
                all_columns = all_columns.union(df.columns)
            df_non_empty = [df for df in df_list if not df.isna().all().all()]
            if df_non_empty:
                self.my_logger.info("Concatenating non-empty DataFrames...")
                df = pd.concat(df_non_empty, ignore_index=True)
                df = df.reindex(columns=all_columns)
                self.my_logger.info(f"Final DataFrame created with shape: {df.shape}")
            else:
                self.my_logger.warning("All DataFrames are empty, creating empty DataFrame")
                df = pd.DataFrame()
                df = df.reindex(columns=all_columns)
            
            self.my_logger.info("create_privacy_plugin_correction completed successfully")
            return df
            
        except Exception as e:
            self.my_logger.error(f"Error in create_privacy_plugin_correction: {str(e)}")
            self.my_logger.error(f"Exception type: {type(e).__name__}")
            self.my_logger.error(f"Full traceback: {traceback.format_exc()}")
            # Return empty DataFrame in case of error
            return pd.DataFrame()


if __name__ == "__main__":
    pd.set_option("display.max_columns", 500)
    pd.set_option("display.max_rows", 500)
