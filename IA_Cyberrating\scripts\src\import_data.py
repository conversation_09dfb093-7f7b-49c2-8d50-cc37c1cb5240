import pandas as pd
import logging
from picklefield import fields
from .utils import db_connexion, safe_merge_with_type_conversion, standardize_data_types


class DataImport:
    """
    A class used to import all the datas

    Attributes
    ----------
    engine : Engine
        a MYSQL engine connection
    ip_type : str
        the ip type (Orange)
    list_ip_type : List[int]
        list of selected IP categories
    scan_date : int
        the analysis period
    ip_penalty_th : int
        the ip penalty threshold considered as vulnerable
    healthy_ip_th : int
        the rating for healthy ip
    removed_sub : List[int]
        list of subsidiary IDs to exclude from analysis
    removed_clust : List[int]
        list of cluster IDs to exclude from analysis
    removed_div : List[int]
        list of division IDs to exclude from analysis
    tenable_plugin : pd.DataFrame
        The dataframe of plugin's information
    ref_rt_asset : pd.DataFrame
        The dataframe of ips of assets
    db_category : pd.DataFrame
        The dataframe of yesterday category rating
    ref_severity : pd.DataFrame
        The dataframe of severity settings
    asset : pd.DataFrame
        The dataframe of assets
    subsidiary : pd.DataFrame
        The dataframe of subsidiary information
    cluster : pd.DataFrame
        The dataframe of cluster information
    division : pd.DataFrame
        The dataframe of division information
    pc_scan_result : pd.DataFrame
        The dataframe of plugins scanned by ip
    mapping : pd.DataFrame
        The dataframe of categories / plugins mapping
    category : pd.DataFrame
        The dataframe of categories information
    family : pd.DataFrame
        The dataframe of families information
    ref_rt : pd.DataFrame
        The dataframe of ip referential
    rating_letters : pd.DataFrame
        The dataframe of ratting letters settings
    table_pc_history : pd.DataFrame
        The dataframe of patching cadence
    table_pc_mean : pd.DataFrame
        patching cadence mean by severity
    table_data_family : pd.DataFrame
        family-level rating data

    Methods
    -------
    get_parameters()
        Loads configuration parameters from the database.
    clean_data()
        Preprocesses and merges various raw data tables.
    import_data()
        Executes all required data loading methods.
    get_tenable_plugin()
        Loads plugin data from Tenable.
    get_pc_history()
        Loads patching cadence history.
    get_pc_mean()
        Loads mean patching cadence per severity.
    get_data_family()
        Loads family-level cyber rating data.
    get_ref_rt_asset()
        Builds IP ↔ asset mapping.
    get_db_category()
        Loads and transforms category-level ratings.
    get_ref_severity()
        Loads severity level configuration.
    get_asset()
        Loads critical asset information.
    get_subsidiary()
        Loads subsidiary details.
    get_cluster()
        Loads cluster details.
    get_division()
        Loads division details.
    get_rating_letters()
        Loads rating letter thresholds.
    """

    def __init__(self, fqdn=False):
        self.engine = db_connexion()
        self.ip_type = None
        self.list_ip_type = None
        self.scan_date = None
        self.ip_penalty_th = None
        self.removed_sub = None
        self.removed_clust = None
        self.removed_div = None
        self.healthy_ip_th = None
        self.tenable_plugin = None
        self.ref_rt_asset = None
        self.db_category = None
        self.ref_severity = None
        self.asset = None
        self.subsidiary = None
        self.cluster = None
        self.division = None
        self.pc_scan_result = None
        self.mapping = None
        self.category = None
        self.family = None
        self.ref_rt = None
        self.rating_letters = None
        self.table_pc_history = None
        self.fqdn_scan_result = None
        self.ref_fqdn = None
        self.get_parameters()
        if not fqdn:
            self.resource_type = 0
            self.import_data()
        else:
            self.resource_type = 1
            self.import_data(fqdn=True)

    def get_tenable_plugin(self) -> None:
        """
        Loads Tenable plugin data from the database and stores it
        in the `tenable_plugin` attribute.
        """

        tenable_plugin = pd.read_sql("SELECT * FROM vulnerability_management_coralystenableplugin", self.engine)

        self.tenable_plugin = tenable_plugin

    def get_pc_history(self, fqdn=False) -> None:
        """
        Loads historical patching cadence data and stores it in `table_pc_history`.

        The data includes information about correction activity by division,
        cluster, subsidiary, and asset.
        """
        my_logger = logging.getLogger("CBR-IMPORT")
        try:
            if not fqdn:
                pc_history = pd.read_sql("SELECT * FROM cyber_rating_patchingcadencenew", self.engine)
            else:
                pc_history = pd.read_sql("SELECT * FROM cyber_rating_patchingcadencenewfqdn", self.engine)
                pc_history = pc_history.rename(columns={"fqdn_id": "ip_id"})

            # Defensive check: handle empty patching cadence history
            if pc_history.empty:
                my_logger.warning(f"{'FQDN' if fqdn else 'IP'} patching cadence history table is empty")
                self.table_pc_history = pd.DataFrame(columns=[
                    'ip_id', 'plugin_id', 'scan_date', 'max_scan_date', 'correction',
                    'correction_delay', 'patching_cadence_days', 'division', 'cluster',
                    'subsidiary', 'asset', 'severity'
                ])
                return

            pc_history.rename(columns={"division_id": "division",
                                       "cluster_id": "cluster",
                                       "subsidiary_id": "subsidiary",
                                       "asset_id": "asset",
                                       "severity_id": "severity"},
                              inplace=True)

            # Defensive data type conversion
            if 'correction' in pc_history.columns:
                pc_history["correction"] = pc_history["correction"].astype(bool)

            # Apply standardized data types for merge compatibility
            pc_history = standardize_data_types(pc_history)

            self.table_pc_history = pc_history
            my_logger.info(f"Successfully loaded pc_history with {len(pc_history)} records")

        except Exception as e:
            my_logger.error(f"Error in get_pc_history: {e}")
            self.table_pc_history = pd.DataFrame(columns=[
                'ip_id', 'plugin_id', 'scan_date', 'max_scan_date', 'correction',
                'correction_delay', 'patching_cadence_days', 'division', 'cluster',
                'subsidiary', 'asset', 'severity'
            ])

    # def get_pc_mean(self) -> None:
    #     """
    #     Loads mean patching cadence per severity level from the database
    #     and stores it in `table_pc_mean`.
    #     """
    #     try:
    #         pc_mean = pd.read_sql("SELECT * FROM cyber_rating_patchingcadenceseveritymean", self.engine)
    #         self.table_pc_mean = pc_mean
    #     except Exception as e:
    #         print(f"Erreur : {e}")

    def get_data_family(self) -> None:
        """
        Loads family-level cyber rating data from the database and stores
        it in `table_data_family`.
        """
        try:
            df_fam = pd.read_sql(f"SELECT * FROM cyber_rating_orangecyberratinggradefamily Where resource_type = {self.resource_type}", self.engine)
            self.table_data_family = df_fam
        except Exception as e:
            print(f"Erreur : {e}")

    def get_ref_rt_asset(self, fqdn=False) -> None:
        """
        Loads and merges IP data with critical asset mappings to build
        the IP / asset relationship. The result is stored in `ref_rt_asset`.
        """
        my_logger = logging.getLogger("CBR-IMPORT")
        try:
            # Defensive check: ensure ref_rt is available and not empty
            if not hasattr(self, 'ref_rt') or self.ref_rt is None or self.ref_rt.empty:
                my_logger.warning("ref_rt is empty or None, creating empty ref_rt_asset")
                self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])
                return

            if not fqdn:
                ref_asset = pd.read_sql("SELECT * FROM inventory_ip_critical_assets", self.engine)
            else:
                ref_asset = pd.read_sql("SELECT * FROM inventory_fqdn_assets", self.engine)
                ref_asset = ref_asset.rename(columns={"fqdn_id": "ip_id"})

            # Defensive check: handle empty ref_asset
            if ref_asset.empty:
                my_logger.warning(f"{'FQDN' if fqdn else 'IP'} critical assets table is empty, creating empty ref_rt_asset")
                self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])
                return

            ref_asset.rename(columns={"criticalasset_id": "asset"}, inplace=True)

            # Defensive check: ensure required columns exist
            required_cols = ["ip_id", "asset"]
            missing_cols = [col for col in required_cols if col not in ref_asset.columns]
            if missing_cols:
                my_logger.warning(f"Missing required columns in ref_asset: {missing_cols}, creating empty ref_rt_asset")
                self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])
                return

            # Use safe merge with automatic type conversion
            ref_rt_asset = safe_merge_with_type_conversion(
                self.ref_rt,
                ref_asset[["ip_id", "asset"]],
                how="inner",
                left_on="id",
                right_on="ip_id")

            # Defensive check: handle empty merge result
            if ref_rt_asset.empty:
                my_logger.warning("Merge between ref_rt and ref_asset resulted in empty DataFrame")
                self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])
            else:
                self.ref_rt_asset = ref_rt_asset
                my_logger.info(f"Successfully created ref_rt_asset with {len(ref_rt_asset)} records")

        except Exception as e:
            my_logger.error(f"Error in get_ref_rt_asset: {e}")
            self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])

    def get_db_category(self) -> None:
        """
        Loads cyber rating data at the asset and subsidiary level for the previous day.

        It merges with organizational metadata to enrich the dataset with division
        and cluster IDs, and stores the final result in `db_category`.
        """

        # Getting distinct subsidiary (or asset) / category couples
        my_logger = logging.getLogger("CBR-IMPORT")
        try:
            query = f"""
            SELECT DISTINCT privacy, level, orange_cyber_rating_category_id, subsidiary_id, orange_cyber_rating_family_id, resource_type
            FROM cyber_rating_orangecyberratinggradecategory
            WHERE level = 'subsidiary' AND privacy != 'all' AND resource_type = {self.resource_type}
            """
            db_sub_category = pd.read_sql(query, self.engine)

            query = f"""
            SELECT DISTINCT privacy, level, orange_cyber_rating_category_id, asset_id, orange_cyber_rating_family_id, resource_type
            FROM cyber_rating_orangecyberratinggradecategory
            WHERE level = 'asset' AND privacy != 'all' AND resource_type = {self.resource_type}
            """
            db_asset_category = pd.read_sql(query, self.engine)

            # Defensive check: handle empty category data
            if db_sub_category.empty and db_asset_category.empty:
                my_logger.warning("Both subsidiary and asset category tables are empty, creating empty db_category")
                self.db_category = pd.DataFrame(columns=[
                    'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type',
                    'cluster', 'division', 'asset', 'group'
                ])
                return

            # Defensive concatenation
            df_list = [db_sub_category, db_asset_category]
            df_non_empty = [df for df in df_list if not df.empty]
            if df_non_empty:
                db_category = pd.concat(df_non_empty, ignore_index=True)
            else:
                my_logger.warning("No category data available, creating empty db_category")
                self.db_category = pd.DataFrame(columns=[
                    'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type',
                    'cluster', 'division', 'asset', 'group'
                ])
                return

            # Reconciling cluster_id and division_id with subsidiary_id
            subsidiary = pd.read_sql("SELECT * FROM organization_subsidiary", self.engine)

            # Defensive check: handle empty subsidiary data
            if subsidiary.empty:
                my_logger.warning("Subsidiary table is empty, cannot reconcile cluster and division IDs")
                self.db_category = pd.DataFrame(columns=[
                    'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type',
                    'cluster', 'division', 'asset', 'group'
                ])
                return

            # Use safe merge with automatic type conversion
            db_category = safe_merge_with_type_conversion(
                db_category,
                subsidiary[["id", "cluster_id", "division_id"]],
                left_on="subsidiary_id",
                right_on="id",
                how="left"
            )

            # Defensive check: handle failed merge
            if db_category.empty:
                my_logger.warning("Merge with subsidiary data resulted in empty DataFrame")
                self.db_category = pd.DataFrame(columns=[
                    'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type',
                    'cluster', 'division', 'asset', 'group'
                ])
                return

            # Cleaning db_category dataframe
            db_category = db_category.drop("id", axis=1, errors='ignore')
            db_category = db_category.assign(group="Orange")

            db_category.rename(
                columns={"division_id": "division",
                         "cluster_id": "cluster",
                         "subsidiary_id": "subsidiary",
                         "asset_id": "asset",
                         "orange_cyber_rating_category_id": "category",
                         "orange_cyber_rating_family_id": "family"},
                inplace=True)

            # Defensive fillna with proper handling of mixed data types
            if 'subsidiary' in db_category.columns and 'cluster' in db_category.columns:
                # Only fill cluster where it's null and subsidiary exists
                mask = db_category['cluster'].isna() & db_category['subsidiary'].notna()
                if mask.any():
                    try:
                        # Convert subsidiary to numeric, handling mixed types safely
                        subsidiary_values = pd.to_numeric(db_category.loc[mask, 'subsidiary'], errors='coerce')
                        # Only update where conversion was successful (not NaN)
                        valid_numeric_mask = mask & ~subsidiary_values.isna()
                        if valid_numeric_mask.any():
                            db_category.loc[valid_numeric_mask, 'cluster'] = 1000 + subsidiary_values[~subsidiary_values.isna()]
                    except Exception as e:
                        my_logger.warning(f"Could not convert subsidiary values for cluster calculation: {e}")
                        # Fallback: set cluster to a default value where it's missing
                        db_category.loc[mask, 'cluster'] = 1000

            # Apply standardized data types to prevent merge issues downstream
            db_category = standardize_data_types(db_category)

            self.db_category = db_category
            my_logger.info(f"Successfully created db_category with {len(db_category)} records")

        except Exception as e:
            my_logger.error(f"Error in get_db_category: {e}")
            self.db_category = pd.DataFrame(columns=[
                'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type',
                'cluster', 'division', 'asset', 'group'
            ])

    def get_ref_severity(self) -> None:
        """
        Loads severity level settings from the database and stores them
        in the `ref_severity` attribute.
        """

        ref_severity = pd.read_sql("SELECT * FROM cyber_rating_orangecyberratingseverity", self.engine)

        self.ref_severity = ref_severity

    def get_asset(self) -> None:
        """
        Loads critical asset information from the database and stores
        it in the `asset` attribute.
        """

        asset = pd.read_sql("SELECT * FROM inventory_criticalasset", self.engine)

        self.asset = asset

    def get_subsidiary(self) -> None:
        """
        Loads subsidiary data from the database and stores it in `subsidiary`.
        """

        subsidiary = pd.read_sql("SELECT * FROM organization_subsidiary", self.engine)

        self.subsidiary = subsidiary

    def get_cluster(self) -> None:
        """
        Loads cluster data from the database and stores it in `cluster`.
        """

        cluster = pd.read_sql("SELECT * FROM organization_cluster", self.engine)

        self.cluster = cluster

    def get_division(self) -> None:
        """
        Loads division data from the database and stores it in `division`.
        """

        division = pd.read_sql("SELECT * FROM organization_division", self.engine)

        self.division = division

    def get_rating_letters(self) -> None:
        """
        Loads rating letter thresholds from the database and stores them
        in the `rating_letters` attribute.
        """

        rating_letters = pd.read_sql("SELECT * FROM cyber_rating_ratinglettersetting", self.engine)
        rating_letters = rating_letters.sort_values(by="value")
        rating_letters = rating_letters.drop(rating_letters.loc[rating_letters["letter"] == "E"].index)

        self.rating_letters = rating_letters

    def get_plugin_to_exclude(self, fqdn=False) -> None:
        try:
            if fqdn:
                df_scan_analysis = pd.read_sql("SELECT * FROM vulnerability_management_pluginscananalysis where status = 1 AND fqdn_id IS NOT NULL", self.engine)
                df_scan_analysis = df_scan_analysis.drop(columns="ip_id")
                df_scan_analysis.rename(columns={"fqdn_id": "ip_id"}, inplace=True)
            else:
                df_scan_analysis = pd.read_sql("SELECT * FROM vulnerability_management_pluginscananalysis where status = 1 AND ip_id IS NOT NULL", self.engine)
            plugin_excluded_from_rating = df_scan_analysis[df_scan_analysis["category"] == 0][["ip_id", "plugin_id"]]
            plugin_excluded_from_pc = df_scan_analysis[df_scan_analysis["category"] == 0][["ip_id", "plugin_id"]]
            # TODO: Anticipation de besoin future après validation (intégration d'autres cat dans l'exclusion)
            # suprimer la ligne précédente
            # plugin_excluded_from_pc = df_scan_analysis[df_scan_analysis["category"].isin([0, 1, 2])][["ip", "plugin"]]
        except Exception as e:
            print(f"Erreur : {e}")
            data = {
                "ip_id": [
                ],
                "plugin_id": [
                ]
            }
            plugin_excluded_from_rating = pd.DataFrame(data)
            data = {
                "ip_id": [
                ],
                "plugin_id": [
                ]
            }
            plugin_excluded_from_pc = pd.DataFrame(data)
        self.plugin_excluded_from_rating = plugin_excluded_from_rating
        self.plugin_excluded_from_pc = plugin_excluded_from_pc

    def import_data(self, fqdn=False) -> None:
        """
        Executes all data loading functions to initialize
        the full dataset used in cyber rating computation.
        """
        self.get_tenable_plugin()
        self.get_ref_severity()
        self.get_asset()
        self.get_subsidiary()
        self.get_cluster()
        self.get_division()
        self.get_rating_letters()
        if fqdn:
            self.clean_fqdn_data()
            self.get_ref_rt_asset(fqdn=True)
            self.get_plugin_to_exclude(fqdn=True)
            self.get_pc_history(fqdn=True)
        else:
            self.clean_ip_data()
            self.get_ref_rt_asset()
            self.get_plugin_to_exclude()
            self.get_pc_history()
            # self.get_pc_mean()
        self.clean_entities_data()
        self.get_db_category()
        self.get_data_family()

    def clean_ip_data(self) -> None:
        """
        Preprocesses and joins raw datasets to prepare them for analysis.

        Operations include:
        - IP / plugin scan merging
        - Filtering IPs by category and activity
        - Loading mappings and classifications
        - Cleaning reference tables (category, family, IPs)
        """

        # Reading databases
        query = """
        SELECT scan_date, ip_id, plugin_id
        FROM vulnerability_management_coralystenablepluginscan
        """
        pc_scan_result = pd.read_sql(query, self.engine)

        query = """
        SELECT division_id, cluster_id, subsidiary_id, id, IP_address, updated_at, last_scanned_at, is_public, category
        FROM inventory_ip
        WHERE category in ({}) and active = 1
        """.format(self.ip_type)
        ip = pd.read_sql(query, self.engine)
        # Reconciling subsidiary_id, cluster_id and division_id with ip_id
        pc_scan_result = pc_scan_result.merge(
            ip[["id", "subsidiary_id", "cluster_id", "division_id"]],
            left_on="ip_id",
            right_on="id",
            how="left",
        )

        pc_scan_result.rename(
            columns={"division_id": "division",
                     "cluster_id": "cluster",
                     "subsidiary_id": "subsidiary"},
            inplace=True)
        pc_scan_result = pc_scan_result.drop("id", axis=1)
        self.pc_scan_result = pc_scan_result

        ip.rename(
            columns={"division_id": "division",
                     "cluster_id": "cluster",
                     "subsidiary_id": "subsidiary"},
            inplace=True)

        # Cleaning ip dataframe
        ip.replace({"cluster": {"\\N": 0}}, inplace=True)
        ip.fillna({"cluster": 0}, inplace=True)
        ip["cluster"] = pd.to_numeric(ip["cluster"], downcast="integer")
        self.ref_rt = ip

    def clean_fqdn_data(self) -> None:
        """
        Preprocesses and joins raw datasets to prepare them for analysis.

        Operations include:
        - FQDN / plugin scan merging
        - Filtering FQDNs by category and activity
        - Loading mappings and classifications
        - Cleaning reference tables (category, family, FQDNs)
        """

        # Reading databases
        query = """
        SELECT scan_date, fqdn_id, plugin_id
        FROM vulnerability_management_coralystenablefqdnpluginscan
        """
        fqdn_scan_result = pd.read_sql(query, self.engine)

        query = """
        SELECT subsidiary_id, id, name, updated_at, last_scanned_at, is_public, category
        FROM inventory_fqdn
        WHERE category in ({}) and is_active = 1
        """.format(self.ip_type)
        fqdn = pd.read_sql(query, self.engine)
        fqdn = fqdn.merge(
            self.subsidiary[["id", "cluster_id", "division_id"]],
            left_on='subsidiary_id',
            right_on='id',
            how="left")
        # Reconciling subsidiary_id, cluster_id and division_id with ip_id
        fqdn_scan_result = fqdn_scan_result.merge(
            fqdn[["id_x", "subsidiary_id", "cluster_id", "division_id"]],
            left_on="fqdn_id",
            right_on="id_x",
            how="left",
        )
        fqdn_scan_result.rename(
            columns={"division_id": "division",
                     "cluster_id": "cluster",
                     "subsidiary_id": "subsidiary"},
            inplace=True)
        fqdn_scan_result = fqdn_scan_result.drop("id_x", axis=1, errors='ignore')
        fqdn_scan_result = fqdn_scan_result.rename(columns={"fqdn_id": "ip_id"})
        self.pc_scan_result = fqdn_scan_result
        fqdn.rename(
            columns={"division_id": "division",
                     "cluster_id": "cluster",
                     "subsidiary_id": "subsidiary"},
            inplace=True)

        # Cleaning fqdn dataframe
        fqdn.replace({"cluster": {"\\N": 0}}, inplace=True)
        fqdn.fillna({"cluster": 0}, inplace=True)
        fqdn["cluster"] = pd.to_numeric(fqdn["cluster"], downcast="integer")
        fqdn = fqdn.drop("id_y", axis=1, errors='ignore')
        fqdn = fqdn.rename(columns={"id_x": "id", "name": "IP_address"})
        self.ref_rt = fqdn

    def clean_entities_data(self) -> None:
        # Reading databases
        map_plugin = pd.read_sql("SELECT * FROM cyber_rating_mappingsourcetoorangecyberrating", self.engine)
        category = pd.read_sql("SELECT * FROM cyber_rating_orangecyberratingcategory", self.engine)
        family = pd.read_sql("SELECT * FROM cyber_rating_orangecyberratingfamily", self.engine)

        # Cleaning mapping dataframe
        na_cat = int(category[category["name"].isin(["À classer", "To be classified"])]["id"].values[0])
        map_plugin.fillna({"orange_cyber_rating_category_id": na_cat}, inplace=True)
        int_cols = ["orange_cyber_rating_category_id", "field_value"]
        map_plugin[int_cols] = map_plugin[int_cols].apply(pd.to_numeric, downcast="integer")
        self.mapping = map_plugin

        # Cleaning category dataframe
        na_fam = int(family[family["name"].isin(["À classer", "To be classified"])]["id"].values[0])
        category.fillna({"orange_cyber_rating_family_id": na_fam}, inplace=True)
        category["orange_cyber_rating_family_id"] = pd.to_numeric(category["orange_cyber_rating_family_id"],
                                                                  downcast="integer")
        self.category = category
        # Cleaning family dataframe
        family["id"] = pd.to_numeric(family["id"], downcast="integer")
        self.family = family

    def get_parameters(self) -> None:
        """
        Loads configuration values from the database via the Constance module,
        and stores them in corresponding attributes.

        Parameters include:
        - IP categories to include
        - Duration of the scan window
        - Penalty threshold for vulnerable IPs
        - Healthy IP threshold
        - IDs of subsidiaries, clusters, and divisions to exclude
        """

        # Reading database
        config = pd.read_sql("SELECT * FROM constance_constance", self.engine)

        try:
            encoded_ip_type = config[config.key == "RATING_IP_CATEGORIES"].value.values[0]
            tmp_ip_type = fields.dbsafe_decode(encoded_ip_type)
            self.list_ip_type = tmp_ip_type.split(",")
            ip_type = str([i.strip() for i in self.list_ip_type])[1:-1]
            self.list_ip_type = [int(x) for x in self.list_ip_type]
        except (Exception,):
            ip_type = "2"
        self.ip_type = ip_type

        try:
            encoded_scan_date = config[config.key == "RATING_DURATION_COMPUTE"].value.values[0]
            scan_date = fields.dbsafe_decode(encoded_scan_date)
        except (Exception,):
            scan_date = 45
        self.scan_date = scan_date

        try:
            encoded_ip_penalty_th = config[config.key == "THRESHOLD_VULNERABLE_IP_PENALTY"].value.values[0]
            ip_penalty_th = fields.dbsafe_decode(encoded_ip_penalty_th)
        except (Exception,):
            ip_penalty_th = 100
        self.ip_penalty_th = ip_penalty_th

        try:
            encoded_remove_sub = config[config.key == "LIST_OF_SUBSIDIARY_ID_TO_REMOVE"].value.values[0]
            removed_sub = fields.dbsafe_decode(encoded_remove_sub)
        except (Exception,):
            removed_sub = ""
        self.removed_sub = removed_sub

        try:
            encoded_remove_clust = config[config.key == "LIST_OF_CLUSTER_ID_TO_REMOVE"].value.values[0]
            removed_clust = fields.dbsafe_decode(encoded_remove_clust)
        except (Exception,):
            removed_clust = ""
        self.removed_clust = removed_clust

        try:
            encoded_remove_div = config[config.key == "LIST_OF_DIVISION_ID_TO_REMOVE"].value.values[0]
            removed_div = fields.dbsafe_decode(encoded_remove_div)
        except (Exception,):
            removed_div = ""
        self.removed_div = removed_div

        try:
            encoded_healthy_ip_th = config[config.key == "THRESHOLD_HEALTHY_IP"].value.values[0]
            healthy_ip_th = fields.dbsafe_decode(encoded_healthy_ip_th)
        except (Exception,):
            healthy_ip_th = 95
        self.healthy_ip_th = healthy_ip_th


if __name__ == "__main__":
    pd.set_option("display.max_columns", 500)
    pd.set_option("display.max_rows", 500)
