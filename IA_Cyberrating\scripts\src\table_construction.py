import os
import math
import numpy as np
import pandas as pd
import logging
from datetime import datetime, date, timedelta
from .utils import BASE_DIR, safe_merge_with_type_conversion, standardize_data_types


class TableConstruction:
    """
    Classe principale pour la construction de tables analytiques à partir
    des résultats de scan de vulnérabilités, selon une politique de confidentialité
    (publique ou privée) et les niveaux organisationnels (division, cluster, subsidiary).

    Cette classe centralise l'ensemble des données nécessaires à la construction de
    tableaux d'analyse (assets, IPs, catégories, historiques de patching, etc.) et
    prépare les structures de travail pour les indicateurs de cybersécurité.

    Parameters
    ----------
    privacy : str
        Filtre les IPs et actifs à prendre en compte("public" ou "private").

    di : object
        Objet contenant les DataFrames et paramètres nécessaires à l'analyse.
        di est une instance de la classe DataImport.

    Attributes
    ----------
    privacy : str
        ("public" ou "private").
    privacy_id : int
        ID de privacy utilisé pour filtrer les IPs (1 pour public, 0 pour private).
    ref_rt_asset : pd.DataFrame
        Données des IPs associées à des assets, filtrées selon la privacy.
    ref_rt : pd.DataFrame
        Données de référence des IPs filtrées selon la privacy.
    pc_penalty_gain : list of int
        Liste des gains de points associés à la correction de vulnérabilités.
    pc_penalty_lose : list of int
        Liste des pertes de points associées aux vulnérabilités détectées.
    list_level : list of str
        Hiérarchie des niveaux organisationnels utilisés pour l'analyse.
    data, pc_data, data_asset, ... : pd.DataFrame or None
        Structures de travail pour les différentes étapes de préparation et d'agrégation
        des données.
    pc_fam : str
        Nom de la famille de métriques traitée ("Patching Cadence").
    """
    def __init__(self, privacy, di):
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")
        self.privacy = privacy
        self.privacy_id = 1 if self.privacy == "public" else 0
        self.ip_type = di.list_ip_type
        self.scan_date = di.scan_date
        self.ip_penalty_th = di.ip_penalty_th
        self.removed_subs = di.removed_sub
        self.removed_clust = di.removed_clust
        self.removed_div = di.removed_div
        self.healthy_ip_th = di.healthy_ip_th
        self.pc_scan_result = di.pc_scan_result

        # Defensive handling of tenable_plugin
        if hasattr(di, 'tenable_plugin') and di.tenable_plugin is not None:
            self.tenable_plugin = di.tenable_plugin
        else:
            my_logger.warning("tenable_plugin is None or missing, creating empty DataFrame")
            self.tenable_plugin = pd.DataFrame(columns=['id', 'name', 'severity'])

        # Defensive handling of ref_rt_asset
        try:
            if hasattr(di, 'ref_rt_asset') and di.ref_rt_asset is not None and not di.ref_rt_asset.empty:
                if "is_public" in di.ref_rt_asset.columns:
                    self.ref_rt_asset = di.ref_rt_asset[di.ref_rt_asset["is_public"] == self.privacy_id]
                else:
                    my_logger.warning("is_public column missing in ref_rt_asset, using all data")
                    self.ref_rt_asset = di.ref_rt_asset
            else:
                my_logger.warning("ref_rt_asset is None or empty, creating empty DataFrame")
                self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])
        except Exception as e:
            my_logger.error(f"Error filtering ref_rt_asset: {e}")
            self.ref_rt_asset = pd.DataFrame(columns=["ip_id", "asset", "id", "is_public"])

        # Defensive handling of db_category
        if hasattr(di, 'db_category') and di.db_category is not None:
            self.db_category = di.db_category
        else:
            my_logger.warning("db_category is None, creating empty DataFrame")
            self.db_category = pd.DataFrame(columns=[
                'privacy', 'level', 'category', 'subsidiary', 'family', 'resource_type',
                'cluster', 'division', 'asset', 'group'
            ])

        # Defensive handling of organizational data
        for attr_name in ['asset', 'subsidiary', 'cluster', 'division', 'category', 'family']:
            attr_value = getattr(di, attr_name, None)
            if attr_value is not None and not attr_value.empty:
                setattr(self, attr_name, attr_value)
            else:
                my_logger.warning(f"{attr_name} is None or empty, creating empty DataFrame")
                setattr(self, attr_name, pd.DataFrame(columns=['id', 'name']))

        # Defensive handling of mapping
        if hasattr(di, 'mapping') and di.mapping is not None:
            self.mapping = di.mapping
        else:
            my_logger.warning("mapping is None, creating empty DataFrame")
            self.mapping = pd.DataFrame(columns=['field_value', 'orange_cyber_rating_category_id', 'orange_cyber_rating_family_id'])

        # Defensive handling of ref_rt
        try:
            if hasattr(di, 'ref_rt') and di.ref_rt is not None and not di.ref_rt.empty:
                if "is_public" in di.ref_rt.columns:
                    self.ref_rt = di.ref_rt[di.ref_rt["is_public"] == self.privacy_id]
                else:
                    my_logger.warning("is_public column missing in ref_rt, using all data")
                    self.ref_rt = di.ref_rt
            else:
                my_logger.warning("ref_rt is None or empty, creating empty DataFrame")
                self.ref_rt = pd.DataFrame(columns=["id", "is_public"])
        except Exception as e:
            my_logger.error(f"Error filtering ref_rt: {e}")
            self.ref_rt = pd.DataFrame(columns=["id", "is_public"])

        # Defensive handling of other attributes
        self.severity = getattr(di, 'ref_severity', pd.DataFrame(columns=['id', 'name']))
        self.table_pc_history = getattr(di, 'table_pc_history', pd.DataFrame())
        self.fqdn_scan_result = getattr(di, 'fqdn_scan_result', pd.DataFrame())
        self.ref_fqdn = getattr(di, 'ref_fqdn', pd.DataFrame())
        self.plugin_excluded_from_rating = getattr(di, 'plugin_excluded_from_rating', pd.DataFrame(columns=['ip_id', 'plugin_id']))
        self.plugin_excluded_from_pc = getattr(di, 'plugin_excluded_from_pc', pd.DataFrame(columns=['ip_id', 'plugin_id']))
        self.pc_penalty_gain = [0, 1, 3, 5]  # # TODO: Add in DB
        self.pc_penalty_lose = [15, 135, 45, 30]  # # TODO: Add in DB
        self.list_level = ["division", "cluster", "subsidiary"]
        self.data = None
        self.pc_data = None
        self.data_asset = None
        self.pc_data_asset = None
        self.data_ip = None
        self.data_ip_asset = None
        self.pc_df = None
        self.pc_df_asset = None
        self.data_category = None
        self.data_family = None
        self.data_total = None
        self.vulnerable_ips = None
        self.pc_db = None
        self.data_rating = None
        self.pc_history = None
        self.pc_fam = "Patching Cadence"

    def get_level_names(self, df):
        """Ajout des informations supplémentaires aux niveaux d'une Ip en combinant plusieurs
        DataFrames (subsidiary, cluster, division, ips) sur la base de leurs clés respectives.

        Args:
            df (pd.DataFrame): DataFrame sur lequel les informations supplémentaires sont ajoutées.

        Returns:
            pd.DataFrame: DataFrame enrichi avec des informations sur les niveaux
            (nom de la filiale, du cluster, de la division et l'adresse IP)
        """
        ips = self.ref_rt[["id", "IP_address"]]
        ips.columns = ["ip_id", "IP_address"]
        subsidiary = self.subsidiary[["id", "long_name"]]
        subsidiary.columns = ["subsidiary", "subsidiary_name"]
        cluster = self.cluster[["id", "name"]]
        cluster.columns = ["cluster", "cluster_name"]
        division = self.division[["id", "long_name", "short_name"]]
        division.columns = ["division", "division_name", "trigram"]

        # Use safe merge operations to prevent data type mismatch errors
        from .utils.utils import safe_merge_with_type_conversion

        df = safe_merge_with_type_conversion(df, subsidiary, on="subsidiary", how="left")
        df = safe_merge_with_type_conversion(df, cluster, on="cluster", how="left")
        df = safe_merge_with_type_conversion(df, division, on="division", how="left")
        df = safe_merge_with_type_conversion(df, ips, on="ip_id", how="left")
        return df

    def get_findings_weight(self, crit):
        """
        Récupère le poids associé par sévérité spécifié.

        Args:
            crit (int): Le niveau de sévérité (1 à 4).

        Returns:
            int: Le poids associé au critère de sévérité spécifié.
        """
        weight = self.severity[self.severity.severity == crit].weight.values[0]
        return weight

    def ip_penalty(self, df):
        """
        Calcule la pénalité d'une adresse IP en fonction du niveau de sévérité de chaque findings
        de vulnérabilité (critique, élevée, moyenne, faible).

        Args:
            df (pd.DataFrame): DataFrame contenant les colonnes de vulnérabilités pour chaque adresse IP.

        Returns:
            float: La pénalité totale pour l'adresse IP basée sur les vulnérabilités et leur sévérité.
        """
        penalty = (self.get_findings_weight(4) * df["critical"]
                   + self.get_findings_weight(3) * df["high"]
                   + self.get_findings_weight(2) * df["medium"]
                   + self.get_findings_weight(1) * df["low"])
        return penalty

    def ip_rating(self, df):
        """Calcule la note d'une adresse IP en fonction de sa pénalité. La note est la différence entre
        100 et la pénalité, avec une valeur minimale de 0.

        Args:
            df (pd.DataFrame): DataFrame contenant les informations de pénalité des adresses IP.

        Returns:
            float: La note de l'adresse IP après application de la pénalité."""
        rating = max(100 - self.ip_penalty(df), 0)
        return rating

    def get_vulnerable_ips(self, df):
        """
        Identifie et filtre les adresses IP les plus vulnérables par rapport à la pénalité,
        puis enrichit le DataFrame avec des informations de niveau (division, cluster, filiale, IP).
        Ce DataFrame fait partie des fichiers exportés par mail tout les jours.

        Args:
            df (pd.DataFrame): DataFrame contenant les données sur les adresses IP et leurs vulnérabilités.

        Returns:
            None: Modifie directement l'attribut `vulnerable_ips` de l'objet avec les IP vulnérables filtrées.
        """
        df = df.drop("scan_date", axis=1)
        df = df.groupby(self.list_level + ["ip_id"], as_index=False).sum()
        df["ip_penalty"] = df.apply(lambda rows: self.ip_penalty(rows), axis=1)
        df = df[df["ip_penalty"] >= self.ip_penalty_th].sort_values(by="ip_penalty", ascending=False)
        df = self.get_level_names(df)

        df = df[["division_name", "cluster_name", "subsidiary_name", "IP_address", "ip_id",
                 "ip_penalty", "info", "low", "medium", "high", "critical"]]

        self.vulnerable_ips = df

    def get_parc_size(self, level, df):
        """
        Calcule la taille du parc pour un niveau donné (asset, groupe, division, cluster, sub) et l'ajoute
        au DataFrame. Si le niveau est "cluster", il traite les clusters en prenant en compte les "faux"
        clusters.

        Args:
            level (str): Le niveau de parc (par exemple, 'asset', 'group', 'cluster').
            df (pd.DataFrame): DataFrame contenant les données sur le niveau de parc à traiter.

        Returns:
            pd.DataFrame: DataFrame enrichi avec la taille du parc pour chaque niveau.
        """
        if level == "asset":
            ref_rt = self.ref_rt_asset
        else:
            ref_rt = self.ref_rt
        ref = ref_rt[(~ref_rt["last_scanned_at"].isnull())]

        if level == "group":
            df["parc_size"] = len(ref)
        elif level != "cluster":
            df_parc = ref.groupby([level]).size().reset_index()
            df_parc.rename(columns={0: "parc_size"}, inplace=True)

            df = df.merge(df_parc[[level, "parc_size"]],
                          on=[level],
                          how="left")
        else:
            fake_cluster = df[level] > 1000
            true_cluster = ~fake_cluster

            df_parc_sub = ref.groupby(["subsidiary"]).size().reset_index()
            df_parc_sub.rename(columns={0: "parc_size"}, inplace=True)
            df_parc_sub["subsidiary"] = df_parc_sub["subsidiary"] + 1000

            df_fake = df[fake_cluster].merge(
                df_parc_sub[["subsidiary", "parc_size"]],
                left_on=level, right_on="subsidiary",
                how="left")

            df_parc_clust = ref.groupby([level]).size().reset_index()
            df_parc_clust.rename(columns={0: "parc_size"}, inplace=True)

            df_true = df[true_cluster].merge(
                df_parc_clust[[level, "parc_size"]],
                on=level,
                how="left")

            df = pd.concat([df_true, df_fake], axis=0)
        df.fillna({"parc_size": 0}, inplace=True)
        return df

    def category_formula(self, rating, weight, size_group, size_parc):
        """
        Calcule la note de catégorie basée sur une formule utilisant la moyenne pondérée des notes,
        la taille du groupe et la taille du parc.

        Args:
            rating (pd.Series): Séries contenant les notes des adresses IP.
            weight (pd.Series): Séries contenant les poids associés aux notes.
            size_group (int): La taille du groupe d'adresses IP.
            size_parc (pd.Series): La taille totale du parc d'adresses IP.

        Returns:
            float: La note de catégorie calculée selon la formule.
        """
        num = (np.average(rating, weights=weight) * math.log(size_group + 1)
               + self.healthy_ip_th * math.log(size_parc.iloc[0] - size_group + 1))

        den = (math.log(size_group + 1)
               + math.log(size_parc.iloc[0] - size_group + 1))

        category_rating = num / den
        return category_rating

    @staticmethod
    def get_issued_ips(df, level, plugin_level, data_ip):
        """
        Calcule et ajoute le nombre d'adresses IP ayant remonté des findings pour chaque niveau spécifié
        dans le DataFrame.

        Args:
            df (pd.DataFrame): DataFrame contenant les informations sur les différents niveaux.
            level (str): Le niveau d'analyse (par exemple, 'group', 'cluster').
            plugin_level (str): Le niveau de plugin pour affiner le calcul ('total' ou un autre niveau spécifique).
            data_ip (pd.DataFrame): DataFrame contenant les données d'adresses IP.

        Returns:
            pd.DataFrame: DataFrame enrichi avec le nombre d'adresses IP vulnérables pour chaque niveau.
        """
        if level == "group":
            df["issued_ips"] = data_ip["ip_id"].nunique()
        else:
            if plugin_level != "total":
                df_issued_ips = data_ip.groupby([level, plugin_level]).size().reset_index()
                df_issued_ips.rename(columns={0: "issued_ips"}, inplace=True)
            else:
                df_issued_ips = data_ip.groupby([level])["ip_id"].nunique().reset_index()
                df_issued_ips.rename(columns={"ip_id": "issued_ips"}, inplace=True)

            if plugin_level != "total":
                df = df.merge(df_issued_ips[[level, plugin_level, "issued_ips"]],
                              on=[level, plugin_level],
                              how="left")
            else:
                df = df.merge(df_issued_ips[[level, "issued_ips"]],
                              on=[level],
                              how="left")
        df.fillna({"issued_ips": 0}, inplace=True)
        return df

    @staticmethod
    def assign_weights(value):
        """
        Assigne un poids à une valeur (sur une échelle de 1 à 100) en fonction de la plage de valeur.
        Plus la note est élevée moins le poids est grand et inversement.

        Args:
            value (float): La valeur pour laquelle le poids doit être attribué.

        Returns:
            int: Le poids
        """
        index = int(value / 10)
        weights = [100, 55, 34, 21, 13, 8, 5, 3, 1, 1]
        return weights[index]

    @staticmethod
    def wa(rating, weight):
        """
        Calcule la moyenne pondérée des notes en utilisant les poids fournis. Si une division par zéro
        se produit, retourne -1.

        Args:
            rating (pd.Series): Les notes des adresses IP à pondérer.
            weight (pd.Series): Les poids associés aux notes.

        Returns:
            float: La moyenne pondérée des notes ou -1 en cas d'erreur (par exemple, division par zéro).
        """
        try:
            return np.average(rating, weights=weight)
        except ZeroDivisionError:
            return -1

    def preparing_data(self):
        """
        Prépare et nettoie les données de vulnérabilités pour l'analyse.

        Cette méthode :
        - Filtre les familles et catégories actives.
        - Fait la correspondance entre plugins et leurs familles/catégories.
        - Met à jour les informations de `table_pc_history`.
        - Filtre les IPs scannées valides.
        - Crée les datasets consolidés pour l'analyse (entities et assets).

            Met à jour les attributs suivants :
            - self.pc_data
            - self.data
            - self.pc_data_asset
            - self.data_asset
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        # Defensive check: ensure required DataFrames are available
        if self.category.empty or self.family.empty:
            my_logger.warning("Category or family data is empty, creating empty datasets")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Defensive check: ensure required columns exist
        required_cat_cols = ["orange_cyber_rating_family_id", "activate"]
        required_fam_cols = ["id", "activate"]

        if not all(col in self.category.columns for col in required_cat_cols):
            my_logger.warning(f"Missing required columns in category: {[col for col in required_cat_cols if col not in self.category.columns]}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        if not all(col in self.family.columns for col in required_fam_cols):
            my_logger.warning(f"Missing required columns in family: {[col for col in required_fam_cols if col not in self.family.columns]}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Filtering active category and family
        try:
            active_cat_fam = safe_merge_with_type_conversion(
                self.category,
                self.family[["id", "activate"]],
                how="inner",
                left_on="orange_cyber_rating_family_id",
                right_on="id")
            active_cat_fam = active_cat_fam[(active_cat_fam["activate_x"] == 1) & (active_cat_fam["activate_y"] == 1)]

            # Defensive check: handle empty active categories/families
            if active_cat_fam.empty:
                my_logger.warning("No active categories/families found, creating empty datasets")
                self.pc_data = pd.DataFrame()
                self.data = pd.DataFrame()
                self.pc_data_asset = pd.DataFrame()
                self.data_asset = pd.DataFrame()
                return
        except Exception as e:
            my_logger.error(f"Error filtering active categories/families: {e}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Defensive check: ensure mapping data is available
        if self.mapping.empty:
            my_logger.warning("Mapping data is empty, creating empty datasets")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Reconciling category / family with the field value (plugin_id)
        try:
            category_family = safe_merge_with_type_conversion(
                self.mapping[["field_value", "orange_cyber_rating_category_id"]],
                active_cat_fam[["id_x", "orange_cyber_rating_family_id"]],
                how="left",
                left_on="orange_cyber_rating_category_id",
                right_on="id_x")
        except Exception as e:
            my_logger.error(f"Error reconciling category/family with mapping: {e}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Defensive check: ensure tenable_plugin data is available
        if self.tenable_plugin.empty:
            my_logger.warning("Tenable plugin data is empty, creating empty datasets")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Reconciling the severity of plugins
        try:
            plugins_info = safe_merge_with_type_conversion(
                self.tenable_plugin[["id", "tenable_plugin_id", "severity"]],
                category_family[["field_value", "orange_cyber_rating_category_id", "orange_cyber_rating_family_id"]],
                how="left",
                left_on="tenable_plugin_id",
                right_on="field_value")
        except Exception as e:
            my_logger.error(f"Error reconciling plugin severity: {e}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Updating category / family for pc_history
        if (self.table_pc_history is not None and not self.table_pc_history.empty):
            try:
                self.table_pc_history.drop("orange_cyber_rating_family_id", axis=1, inplace=True, errors="ignore")
                self.table_pc_history.drop("orange_cyber_rating_category_id", axis=1, inplace=True, errors="ignore")
                self.table_pc_history = self.table_pc_history.merge(plugins_info[["id", "orange_cyber_rating_family_id",
                                                                                  "orange_cyber_rating_category_id"]],
                                                                    how="left",
                                                                    left_on="plugin_id",
                                                                    right_on="id")
                self.table_pc_history.drop("id_x", axis=1, inplace=True, errors="ignore")
                self.table_pc_history.drop("id_y", axis=1, inplace=True, errors="ignore")
            except Exception as e:
                my_logger.error(f"Error updating pc_history: {e}")

        # Defensive check: ensure pc_scan_result and ref_rt are available
        if not hasattr(self, 'pc_scan_result') or self.pc_scan_result.empty:
            my_logger.warning("pc_scan_result is empty, creating empty datasets")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        if self.ref_rt.empty:
            my_logger.warning("ref_rt is empty, creating empty datasets")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Filtering scans on right ips (active / Orange / right privacy)
        try:
            pc_data = safe_merge_with_type_conversion(
                self.pc_scan_result,
                self.ref_rt[["id"]],
                how="inner",
                left_on="ip_id",
                right_on="id")

            # Defensive check: handle empty merge result
            if pc_data.empty:
                my_logger.warning("No scan data matches reference IPs, creating empty datasets")
                self.pc_data = pd.DataFrame()
                self.data = pd.DataFrame()
                self.pc_data_asset = pd.DataFrame()
                self.data_asset = pd.DataFrame()
                return
        except Exception as e:
            my_logger.error(f"Error filtering scans: {e}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Reconciling information of plugins for each scans
        try:
            pc_data = safe_merge_with_type_conversion(
                pc_data,
                plugins_info[["id", "severity", "orange_cyber_rating_family_id", "orange_cyber_rating_category_id"]],
                how="left",
                left_on="plugin_id",
                right_on="id")
        except Exception as e:
            my_logger.error(f"Error reconciling plugin information: {e}")
            self.pc_data = pd.DataFrame()
            self.data = pd.DataFrame()
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()
            return

        # Refactoring cluster and privacy variables
        try:
            # Defensive fillna with proper handling of empty subsidiary column
            if 'subsidiary' in pc_data.columns and 'cluster' in pc_data.columns:
                mask = pc_data['cluster'].isna() & pc_data['subsidiary'].notna()
                if mask.any():
                    pc_data.loc[mask, 'cluster'] = 1000 + pc_data.loc[mask, 'subsidiary']

            if 'cluster' in pc_data.columns:
                pc_data["cluster"] = pd.to_numeric(pc_data["cluster"], downcast="integer", errors='coerce')
            pc_data["privacy"] = self.privacy
        except Exception as e:
            my_logger.error(f"Error refactoring cluster and privacy variables: {e}")

        # Assigning patching_cadence dataframe
        try:
            required_cols = ["scan_date", "division", "cluster", "subsidiary", "ip_id", "privacy", "plugin_id",
                           "severity", "orange_cyber_rating_family_id", "orange_cyber_rating_category_id"]
            available_cols = [col for col in required_cols if col in pc_data.columns]
            self.pc_data = pc_data[available_cols] if available_cols else pd.DataFrame()
        except Exception as e:
            my_logger.error(f"Error creating pc_data: {e}")
            self.pc_data = pd.DataFrame()

        # Assigning all-data dataframe
        try:
            if not self.pc_data.empty and 'scan_date' in self.pc_data.columns:
                self.data = self.pc_data[self.pc_data["scan_date"] > (date.today() - timedelta(days=self.scan_date))]
            else:
                self.data = pd.DataFrame()
        except Exception as e:
            my_logger.error(f"Error filtering data by scan date: {e}")
            self.data = pd.DataFrame()

        # Filter plugins to exclude from the rating
        try:
            if not self.data.empty and not self.plugin_excluded_from_rating.empty:
                # Use safe merge to handle data type mismatches
                merged_data = safe_merge_with_type_conversion(
                    self.data,
                    self.plugin_excluded_from_rating,
                    on=["ip_id", "plugin_id"],
                    how="left",
                    indicator=True
                )
                if not merged_data.empty and '_merge' in merged_data.columns:
                    self.data = merged_data.query('_merge == "left_only"').drop(columns="_merge")
                else:
                    # If merge failed, keep original data
                    my_logger.warning("Plugin exclusion merge failed, keeping original data")
        except Exception as e:
            my_logger.error(f"Error filtering excluded plugins: {e}")

        # Creating data asset based on ip_asset referential
        try:
            if not pc_data.empty and not self.ref_rt_asset.empty:
                asset_cols = ["scan_date", "ip_id", "plugin_id", "severity",
                             "orange_cyber_rating_family_id", "orange_cyber_rating_category_id"]
                available_asset_cols = [col for col in asset_cols if col in pc_data.columns]

                if available_asset_cols:
                    pc_data_asset = safe_merge_with_type_conversion(
                        pc_data[available_asset_cols],
                        self.ref_rt_asset[["ip_id", "asset"]],
                        how="inner",
                        on="ip_id")
                    pc_data_asset["privacy"] = self.privacy

                    self.pc_data_asset = pc_data_asset
                    if 'scan_date' in pc_data_asset.columns:
                        self.data_asset = pc_data_asset[pc_data_asset["scan_date"] > (date.today() - timedelta(days=self.scan_date))]
                    else:
                        self.data_asset = pc_data_asset
                else:
                    self.pc_data_asset = pd.DataFrame()
                    self.data_asset = pd.DataFrame()
            else:
                self.pc_data_asset = pd.DataFrame()
                self.data_asset = pd.DataFrame()
        except Exception as e:
            my_logger.error(f"Error creating asset data: {e}")
            self.pc_data_asset = pd.DataFrame()
            self.data_asset = pd.DataFrame()

        # Filter plugins to exclude from asset data rating
        try:
            if not self.data_asset.empty and not self.plugin_excluded_from_rating.empty:
                # Use safe merge to handle data type mismatches
                merged_asset_data = safe_merge_with_type_conversion(
                    self.data_asset,
                    self.plugin_excluded_from_rating,
                    on=["ip_id", "plugin_id"],
                    how="left",
                    indicator=True
                )
                if not merged_asset_data.empty and '_merge' in merged_asset_data.columns:
                    self.data_asset = merged_asset_data.query('_merge == "left_only"').drop(columns="_merge")
                else:
                    # If merge failed, keep original data
                    my_logger.warning("Asset plugin exclusion merge failed, keeping original data")
        except Exception as e:
            my_logger.error(f"Error filtering excluded plugins from asset data: {e}")

    def creating_data_ip(self, entities_level):
        """
        Crée un DataFrame contenant les IPs avec vulnérabilités, par entité ou par asset.

        Args:
            str: Niveau de regroupement ('entities' ou 'asset').

        Returns:
            None
                Met à jour les attributs :
                - self.data_ip ou self.data_ip_asset (selon le niveau)
                - self.data_rating
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        if entities_level == "entities":
            entities_list = self.list_level
            level = "subsidiary"
            df = self.data
        else:
            entities_list = ["asset"]
            level = "asset"
            df = self.data_asset

        # Defensive check: handle empty DataFrame
        if df.empty:
            my_logger.warning(f"Input DataFrame is empty for {entities_level} level")
            if entities_level == "entities":
                self.data_ip = pd.DataFrame()
            else:
                self.data_ip_asset = pd.DataFrame()
            return

        # Defensive check: ensure scan_date column exists
        if 'scan_date' not in df.columns:
            my_logger.warning(f"scan_date column missing in DataFrame for {entities_level} level")
            if entities_level == "entities":
                self.data_ip = pd.DataFrame()
            else:
                self.data_ip_asset = pd.DataFrame()
            return

        # Defensive check: ensure required columns exist
        required_cols = entities_list + ["ip_id", "plugin_id", "orange_cyber_rating_family_id", "orange_cyber_rating_category_id"]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            my_logger.warning(f"Missing required columns for {entities_level}: {missing_cols}")
            if entities_level == "entities":
                self.data_ip = pd.DataFrame()
            else:
                self.data_ip_asset = pd.DataFrame()
            return

        try:
            data = df.sort_values(by="scan_date", ascending=False).groupby(
                entities_list + ["ip_id", "plugin_id", "orange_cyber_rating_family_id",
                                 "orange_cyber_rating_category_id"]).head(1).reset_index()
            data.sort_values(by=entities_list + ["ip_id", "plugin_id"], inplace=True)
        except Exception as e:
            my_logger.error(f"Error sorting and grouping data for {entities_level}: {e}")
            if entities_level == "entities":
                self.data_ip = pd.DataFrame()
            else:
                self.data_ip_asset = pd.DataFrame()
            return

        # Defensive check: ensure severity column exists
        if 'severity' not in data.columns:
            my_logger.warning(f"severity column missing in data for {entities_level}")
            if entities_level == "entities":
                self.data_ip = pd.DataFrame()
            else:
                self.data_ip_asset = pd.DataFrame()
            return

        try:
            # Creating severity columns
            data = pd.pivot_table(data,
                                  index=entities_list + ["scan_date",
                                                         "orange_cyber_rating_family_id",
                                                         "orange_cyber_rating_category_id",
                                                         "ip_id"],
                                  columns="severity",
                                  aggfunc="size", fill_value=0).reset_index()

            # Defensive check: ensure severity data is available
            if not hasattr(self, 'severity') or self.severity.empty:
                my_logger.warning(f"Severity data is empty for {entities_level}")
                # Create default severity columns
                for i in range(5):  # 0-4 severity levels
                    if i not in data.columns:
                        data[i] = 0
            else:
                list_severity = self.severity["severity"].to_list()
                for severity_level in list_severity:
                    if severity_level not in data.columns:
                        data[severity_level] = 0

            # Defensive check: ensure severity columns exist before filtering
            severity_cols = [col for col in [1, 2, 3, 4] if col in data.columns]
            if severity_cols:
                # Filtering on ips with issues
                data = data[data[severity_cols].any(axis=1)]
            else:
                my_logger.warning(f"No severity columns found for filtering in {entities_level}")

            # Defensive check: ensure data is not empty after filtering
            if data.empty:
                my_logger.warning(f"No data remaining after severity filtering for {entities_level}")
                if entities_level == "entities":
                    self.data_ip = pd.DataFrame()
                else:
                    self.data_ip_asset = pd.DataFrame()
                return

            # Grouping on ip / category, sum the number of plugins and get the max scan date
            agg_dict = {}
            for i in range(5):  # 0-4 severity levels
                if i in data.columns:
                    agg_dict[i] = "sum"
            if 'scan_date' in data.columns:
                agg_dict["scan_date"] = "max"

            if agg_dict:
                data_ip = (
                    data.groupby(
                        entities_list
                        + ["ip_id", "orange_cyber_rating_category_id", "orange_cyber_rating_family_id"]
                    )
                    .agg(agg_dict)
                    .reset_index()
                )
            else:
                my_logger.warning(f"No aggregation columns available for {entities_level}")
                if entities_level == "entities":
                    self.data_ip = pd.DataFrame()
                else:
                    self.data_ip_asset = pd.DataFrame()
                return

        except Exception as e:
            my_logger.error(f"Error creating severity pivot table for {entities_level}: {e}")
            if entities_level == "entities":
                self.data_ip = pd.DataFrame()
            else:
                self.data_ip_asset = pd.DataFrame()
            return

        # Renaming dataframe's column
        data_ip.rename(columns={0: "info", 1: "low", 2: "medium",
                                3: "high", 4: "critical",
                                "orange_cyber_rating_family_id": "family",
                                "orange_cyber_rating_category_id": "category"
                                }, inplace=True)

        if not data_ip.empty:
            if entities_level == "entities":
                self.get_vulnerable_ips(data_ip)

            # Factoring "parc_size" and "issued_ips" columns
            data_ip = self.get_issued_ips(data_ip, level, "category", data_ip)
            data_ip = self.get_parc_size(level, data_ip)

            # Factoring "ip_rating" column
            data_ip["ip_rating"] = data_ip.apply(lambda rows: self.ip_rating(rows), axis=1)

            # Assigning weights on ips rating
            data_ip["ip_rating_weight"] = data_ip["ip_rating"].apply(self.assign_weights)

            self.data_rating = data_ip[entities_list + ["ip_id", "category", "family",
                                                        "info", "low", "medium", "high",
                                                        "critical", "scan_date", "ip_rating"]]

        if entities_level == "entities":
            self.data_ip = data_ip.copy()
            if data_ip is not None and not data_ip.empty:
                self.data_ip_grade = data_ip[["ip_id", "category", "family", "ip_rating"]]
                self.data_ip_grade.rename(columns={"ip_rating": "rating"})
            else:
                self.data_ip_grade = pd.DataFrame(columns=["ip_id", "category", "family", "rating"])
        else:
            self.data_ip_asset = data_ip

    def duplicate_yesterday_data(self, df, level):
        """
        Duplique les données de la veille pour les catégories n'ayant pas remonté de nouveaux findings.

        Args:
            df (pd.DataFrame): Données actuelles de la catégorie PC à compléter.
            level (str): Niveau de regroupement ('subsidiary' ou 'asset').

        Returns:
            pd.DataFrame: Données enrichies avec les anciennes catégories (hors Patching Cadence).
        """
        if level == "asset":
            data_ip = self.data_ip_asset
        else:
            data_ip = self.data_ip

        pc_category = self.category[self.category["name"] == self.pc_fam].id.values[0]
        db_category = self.db_category[(self.db_category["level"] == level)
                                       & (self.db_category["privacy"] == self.privacy)
                                       & (self.db_category["category"] != pc_category)]

        db_category = self.get_parc_size(level, db_category)
        db_category = self.get_issued_ips(db_category, level, "category", data_ip)
        db_category = db_category[db_category["parc_size"] != 0]

        if not db_category.empty:
            merged_df = df.merge(db_category,
                                 on=(["privacy", "level", "group"]
                                     + ["division", "subsidiary"]
                                     + ["asset", "family", "category"]),
                                 how="outer")

            duplicated_columns = ["parc_size", "issued_ips", "cluster"]
            for col in duplicated_columns:
                col_x = col + "_x"
                col_y = col + "_y"
                merged_df[col] = merged_df[col_x].astype("float").fillna(merged_df[col_y])
            merged_df["category_rating"] = merged_df["category_rating"].astype("float")
            merged_df.fillna({"category_rating": self.healthy_ip_th}, inplace=True)
            data_category = merged_df[
                ["privacy", "level", "group"] +
                self.list_level +
                ["asset", "family", "category", "category_rating", "parc_size", "issued_ips"]]
        else:
            data_category = df
        return data_category

    def create_first_level(self, df, entities_level):
        """
        Calcule les scores de catégories au premier niveau d'analyse (subsidiary ou asset).

        Args:
            df (pd.DataFrame): Données consolidées d'IP avec leurs ratings.
            entities_level (str): Niveau de regroupement ('subsidiary' ou 'asset').

        Returns:
            pd.DataFrame: Données agrégées avec score de catégorie, taille parc, ...
        """
        if entities_level == "subsidiary":
            entities_list = self.list_level
        else:
            entities_list = ["asset"]

        if not df.empty:
            data_first_level = (
                df.groupby(entities_list + ["family", "category", "parc_size"])
                .apply(
                    lambda row: pd.Series(
                        {
                            "category_rating": self.category_formula(
                                row["ip_rating"],
                                row["ip_rating_weight"],
                                len(row["ip_rating"]),
                                row["parc_size"]),
                            "issued_ips": row["issued_ips"].iloc[0],
                            "max_scan_date": max(row["scan_date"])
                        }
                    )
                )
                .reset_index()
            )

            # Factoring data_category_sub dataframe
            if entities_level == "subsidiary":
                data_first_level["group"] = "Orange"
                data_first_level["privacy"] = self.privacy
                data_first_level["level"] = "subsidiary"
                data_first_level["asset"] = np.nan
            else:
                data_first_level["group"] = "Orange"
                data_first_level["privacy"] = self.privacy
                data_first_level["level"] = "asset"
                data_first_level["subsidiary"] = np.nan
                data_first_level["cluster"] = np.nan
                data_first_level["division"] = np.nan

            data_first_level = data_first_level[
                ["privacy", "level", "group"] +
                self.list_level +
                ["asset", "family", "category", "category_rating", "parc_size", "issued_ips"]]

        else:
            data_first_level = pd.DataFrame(
                columns=["privacy", "level", "group"]
                + self.list_level
                + ["asset", "family", "category", "category_rating", "parc_size", "issued_ips"]
            )

        data_first_level = self.duplicate_yesterday_data(data_first_level, entities_level)
        data_first_level = self.remove_entity(data_first_level, "subsidiary")
        # # INFO: La ligne suivante permet de tester les scrits dans un env similaire à
        # la prod sans IP privées.
        # data_first_level = data_first_level[data_first_level["privacy"] == "public"]
        return data_first_level

    def remove_entity(self, df, level):
        """
        Marque les entités à exclure (subsidiary, cluster ou division)
        en fonction des paramètres définis en base.

        Args:
            df (pd.DataFrame): Données contenant les entités à vérifier.
            level (str): Niveau d'entité à exclure ('subsidiary', 'cluster', 'division').

        Returns:
            pd.DataFrame: DataFrame enrichi avec une colonne booléenne indiquant les entités à retirer.
        """
        if level == "subsidiary":
            self.remove_subs = [int(sub.strip()) for sub in self.removed_subs.split(",") if sub.strip().isdecimal()]
            df["remove_subsidiary"] = df["subsidiary"].isin(self.remove_subs)

        elif level == "cluster":
            self.remove_clust = [int(sub.strip()) for sub in self.removed_clust.split(",") if sub.strip().isdecimal()]
            df["remove_cluster"] = df["cluster"].isin(self.remove_clust)

        elif level == "division":
            self.remove_div = [int(sub.strip()) for sub in self.removed_div.split(",") if sub.strip().isdecimal()]
            df["remove_division"] = df["division"].isin(self.remove_div)

        return df

    def calculate_up_plugin_level(self, plugin_level, df, entities_level):

        if entities_level == "subsidiary":
            entities_list = self.list_level
            level = "subsidiary"
            data_ip = self.data_ip
        else:
            entities_list = ["asset"]
            level = "asset"
            data_ip = self.data_ip_asset

        if plugin_level == "family":
            plugin_list = [plugin_level]
            plugin_list_rating = [plugin_level, plugin_level + "_rating"]
            df = safe_merge_with_type_conversion(
                df,
                self.category[["orange_cyber_rating_family_id", "id", "weight"]],
                left_on=["family", "category"],
                right_on=["orange_cyber_rating_family_id", "id"],
                how="left")
            df.rename(columns={"category_rating": plugin_level + "_rating"},
                      inplace=True)

        elif plugin_level == "category":
            plugin_list = [plugin_level, "family"]
            plugin_list_rating = ["family", plugin_level,
                                  plugin_level + "_rating"]
        else:
            plugin_list = []
            plugin_list_rating = [plugin_level + "_rating"]
            df = safe_merge_with_type_conversion(
                df,
                self.family[["id", "weight"]],
                left_on="family",
                right_on="id",
                how="left")
            df.rename(columns={"family_rating": plugin_level + "_rating"},
                      inplace=True)

        if not df.empty:
            list_column = ["group"] + entities_list + plugin_list_rating + ["weight"]
            list_gb = ["group"] + entities_list + plugin_list
            df = df[list_column].groupby(list_gb).apply(
                lambda x: pd.Series(
                    {plugin_level + "_rating": self.wa(x[plugin_level + "_rating"],
                                                       x["weight"])})).reset_index()

            df = self.get_issued_ips(df, level, plugin_level, data_ip)
            df = self.get_parc_size(level, df)

            df["privacy"] = self.privacy
            if entities_level == "subsidiary":
                df["level"] = "subsidiary"
                df["asset"] = np.nan
            else:
                df["level"] = "asset"
                df["subsidiary"] = np.nan
                df["cluster"] = np.nan
                df["division"] = np.nan

            df = df[["privacy", "level", "group"] + self.list_level + ["asset"]
                    + plugin_list_rating + ["parc_size", "issued_ips"]]

        else:
            df = pd.DataFrame(
                columns=["privacy", "level", "group"]
                + self.list_level
                + ["asset"]
                + plugin_list_rating
                + ["parc_size", "issued_ips"]
            )
        return df

    def calculate_up_entity(self, level, plugin_level, df):
        if plugin_level == "family":
            plugin_list = [plugin_level]
        elif plugin_level == "category":
            plugin_list = [plugin_level, "family"]
        else:
            plugin_list = []

        list_all_levels = ["group"] + self.list_level
        list_gb = list_all_levels[:list_all_levels.index(level) + 1] + plugin_list
        list_column = list_gb + [plugin_level + "_rating", "parc_size"]
        if not df.empty:
            df = df[~df["remove_" + list_all_levels[list_all_levels.index(level) + 1]]]
            df = df[list_column].groupby(list_gb).apply(lambda x: pd.Series(
                {plugin_level + "_rating": self.log_wa(x[plugin_level + "_rating"], x["parc_size"]),
                 "parc_size_sum": (x["parc_size"] + 1).apply(math.log).sum()}))
            if not df.empty:
                df = df.reset_index()
            df = self.get_parc_size(level, df)
            df = self.get_issued_ips(df, level, plugin_level, self.data_ip)
            df = self.remove_entity(df, level)
            df["privacy"] = self.privacy
            df["level"] = level
            df["asset"] = np.nan
            for sub_level in list_all_levels[list_all_levels.index(level) + 1:]:
                df[sub_level] = np.nan
        else:
            df["parc_size_sum"] = None
        return df

    def prepare_patching_cadence(self, entities_level):
        """
        Prépare et met à jour la table de suivi du temps de correction des vulnérabilités (patching cadence).

        Cette méthode centralise la logique de calcul du temps de correction des vulnérabilités par entité (organisation ou asset).
        Elle suit l'évolution des vulnérabilités détectées via les scans successifs, identifie les corrections réalisées,
        calcule les délais de correction, et alimente un historique exploitable pour le calcul de la note de sécurité.

        Fonctionnalités principales :
        - Génère ou met à jour l'historique des vulnérabilités par couple (ip_id, plugin_id).
        - Applique une logique de correction basée sur le nombre de scans post-détection (>= 2).
        - Corrige et nettoie les données historiques (suppression des lignes obsolètes, ajustement des dates).
        - Enrichit les données avec les métadonnées de niveau "asset" ou "entities" (cluster, division, etc.).
        - Calcule le délai de correction et le compare au temps de corection accordé par niveau de sévérité.
        - Produit un historique consolidé prêt à être utilisé pour le cyber rating.

        Args:
            entities_level (str): Niveau d'agrégation souhaité :
                - "entities" : organisation (subsidiary, cluster, division)
                - "asset"    : machine ou service spécifique

        Modifie :
            - self.pc_df / self.pc_df_asset : table finale filtrée et enrichie, prête pour la notation.
            - self.pc_history : historique mis à jour pour suivi temporel.
        """
        if entities_level == "entities":
            pc_df = self.pc_data
            entities_list = self.list_level
            ref_rt = self.ref_rt
        else:
            pc_df = self.pc_data_asset
            entities_list = ["asset"]
            ref_rt = self.ref_rt_asset

        data_pc = pc_df[pc_df.severity != 0]
        data_pc = data_pc.assign(min_scan_date=data_pc["scan_date"])
        data_pc = data_pc.assign(max_scan_date=data_pc["scan_date"])

        def create_pc_history_table(group):
            """
            Calcule l'historique de correction pour un couple (ip_id, plugin_id).

            Cette fonction identifie les périodes de présence d'un plugin de sévérité non nulle sur une IP,
            détermine le nombre de scans effectués entre les dates d'apparition, et estime les délais de correction.

            Args:
                group (pd.DataFrame): Données groupées selon le couple (ip_id, plugin_id).

            Returns:
                pd.DataFrame: Résumé contenant :
                    - min_scan_date : première apparition du plugin
                    - max_scan_date : dernière apparition
                    - correction_delay : durée entre apparition et correction
                    - correction : bool indiquant si le plugin a été corrigé
            """
            scan_count = []
            day_count = []
            data_ip = pc_df[pc_df["ip_id"] == group["ip_id"].values[0]]
            for i in range(len(group) - 1):
                start_date = group.iloc[i]["scan_date"]
                end_date = group.iloc[i + 1]["scan_date"]
                scan_count.append(data_ip[(data_ip["scan_date"] > start_date) &
                                          (data_ip["scan_date"] <= end_date)]["scan_date"].nunique())

            scan_count.append(data_ip[data_ip["scan_date"] > max(group["scan_date"])]["scan_date"].nunique())
            if len(group) > 1:
                day_count = list(group["scan_date"].diff().dt.days)
                del day_count[0]
            day_count.append((max(data_ip["scan_date"]) - max(group["scan_date"])).days)

            group["count"] = [True if elem >= 2 else False for elem in scan_count]
            group["correction_delay"] = day_count
            group["count2"] = scan_count
            group["group"] = (group["count"] != group["count"].shift()).cumsum()
            if not all(group["count"]):
                filtered_df = group[~group["count"]]
            else:
                filtered_df = group[group["count"]]
                filtered_df["correction_delay"] = 0
            result = filtered_df.groupby(
                entities_list +
                ["ip_id", "plugin_id", "severity",
                 "orange_cyber_rating_family_id",
                 "orange_cyber_rating_category_id", "group"]
            ).agg({"correction_delay": "sum", "min_scan_date": "min", "max_scan_date": "max"}).reset_index()
            result["correction"] = [True] * (len(result) - 1) + [group["count"].values[-1]]
            result['max_scan_date'] = pd.to_datetime(result['max_scan_date'])
            result['min_scan_date'] = pd.to_datetime(result['min_scan_date'])
            result.loc[:, "correction_delay"] = np.where(
                result["correction"],
                (result['max_scan_date'] - result["min_scan_date"]).dt.days,
                result["correction_delay"])
            return result[["min_scan_date", "max_scan_date", "correction_delay", "correction"]]

        def filter_old_data(history_table):
            """
            Nettoie et met à jour les dates des données historiques obsolètes.

            - Supprime les plugins corrigés depuis plus de 12 mois.
            - Pour les vulnérabilités présentes depuis plus de 24 mois, tronque la min_scan_date à 24 mois.

            Args:
                history_table (pd.DataFrame): Table d'historique contenant les colonnes de dates.

            Returns:
                pd.DataFrame: Historique filtré et ajusté.
            """
            history_table = history_table.copy()
            history_table['min_scan_date'] = pd.to_datetime(history_table['min_scan_date'])
            history_table['max_scan_date'] = pd.to_datetime(history_table['max_scan_date'])

            # Supprimer les corrections de plus de 12 mois (n'impactent plus la notation).
            cutoff_date = datetime.today() - pd.DateOffset(months=12)
            history_table = history_table[~((history_table["correction"] == 1) & (history_table["max_scan_date"] < cutoff_date))]

            # Filtrer les entrées plus anciennes que 24 mois
            date_seuil = pd.to_datetime((datetime.today() - pd.DateOffset(months=24)).normalize())
            df_filtered = history_table[history_table['min_scan_date'] < date_seuil].copy()
            df_filtered['min_scan_date'] = date_seuil
            df_filtered['max_scan_date'] = df_filtered[['max_scan_date', 'min_scan_date']].max(axis=1)
            df_filtered['correction_delay'] = (df_filtered['max_scan_date'] - df_filtered['min_scan_date']).dt.days

            history_table.update(df_filtered)
            return history_table

        def update_entities(history_table, entities_level):
            """Mettre à jour les informations d'entité selon le niveau choisi (asset ou organisation).

            Pour le niveau asset :
            - Filtre sur les lignes possédant une valeur non nulle pour 'asset'
            - Jointure avec la table de référence ref_rt_asset pour compléter les informations.

            Pour le niveau organisation (cluster/subsidiary/division) :
            - Filtre sur les lignes sans information asset.
            - Jointure avec la table ref_rt pour récupérer subsidiary, cluster, division et last_scanned_at.
            - Remplace les valeurs manquantes de cluster par 1000 + subsidiary.

            Args:
                history_table (pd.DataFrame): Données historiques à enrichir.
                entities_level (str): Niveau d'analyse ("asset" ou "entities").

            Returns:
                pd.DataFrame: Table enrichie avec les métadonnées d'entités.
            """
            if entities_level == "asset":
                history_table = history_table[history_table["asset"].notna()]
                history_table = history_table.merge(self.ref_rt_asset[["ip_id", "asset"]],
                                                    how="left",
                                                    on="ip_id",
                                                    suffixes=('_old', ''))
                history_table.drop(columns="asset_old",
                                   axis=1,
                                   inplace=True,
                                   errors="ignore")
            else:
                history_table = history_table[~history_table["asset"].notna()]
                history_table.merge(
                    self.ref_rt[["id", "subsidiary", "cluster", "division"]],
                    left_on="ip_id",
                    right_on="id",
                    how="left",
                    suffixes=('_old', ''))
                history_table = history_table.copy()
                history_table.drop(columns=["subsidiary_old",
                                            "cluster_old",
                                            "division_old"],
                                   axis=1,
                                   inplace=True,
                                   errors="ignore")
                history_table = history_table.fillna(
                    {"cluster": 1000 + history_table["subsidiary"]})
                history_table["cluster"] = pd.to_numeric(history_table["cluster"],
                                                         downcast="integer")
            return history_table

        def maj_pc_history_table(history_table_i, pc_df, privacy):
            """
            Met à jour la table d'historique des vulnérabilités associées aux couples (ip_id, plugin_id)
            en se basant sur les nouveaux résultats de scans et les politiques de correction.

            La méthode applique les étapes suivantes :
            - Suppression des anciennes données et mise à jour des entités.
            - Fusion avec les nouvelles données pour identifier les vulnérabilités rescannées.
            - Détection et mise à jour des vulnérabilités corrigées (après 2 scans post-détection).
            - Calcul du délai de correction.
            - Ajout de nouvelles vulnérabilités détectées à l'historique.

            Args:
                history_table_i (pd.DataFrame): Table d'historique initiale contenant
                les vulnérabilités détectées

                pc_df (pd.DataFrame): Données brutes issues des scans récents contenant
                les vulnérabilités détectéeset leurs dates associées.

                privacy (str): ("public", "private").

            Returns:
            pd.DataFrame: La nouvelle table d'historique contenant :
                - les anciennes vulnérabilités avec statut corrigé ou non mis à jour,
                - les nouveaux couples vulnérables ajoutés,
                - les délais de correction mis à jour,
                - une colonne "timestamp" correspondant à la date de mise à jour.
            """
            timestamp = history_table_i["timestamp"].values[0] - timedelta(days=30)
            history_table = history_table_i[history_table_i["privacy"] == privacy]
            history_table = filter_old_data(history_table)
            history_table = update_entities(history_table, entities_level)

            data_pc_2 = pc_df.merge(self.ref_rt[["id", "last_scanned_at"]], left_on="ip_id", right_on="id", how="left")
            data_pc_2 = data_pc_2[data_pc_2["severity"] != 0]
            data_pc_2 = data_pc_2[(data_pc_2["scan_date"] >= timestamp)]
            colonne_cle = entities_list + ["ip_id", "plugin_id", "severity", "privacy",
                                           "orange_cyber_rating_family_id",
                                           "orange_cyber_rating_category_id"]
            last_max = history_table_i.groupby(
                colonne_cle
                )["max_scan_date"] \
                .max() \
                .reset_index() \
                .rename(columns={"max_scan_date": "prev_max_scan"})
            data_pc_2 = data_pc_2.merge(
                last_max,
                on=colonne_cle,
                how="left"
            )
            # ne garder que ce qui est strictement postérieur
            data_pc_2 = data_pc_2[
                data_pc_2["scan_date"] > data_pc_2["prev_max_scan"].fillna(timestamp)
            ]
            # (optionnel) on peut maintenant supprimer la colonne prev_max_scan
            data_ip = data_pc_2.drop(columns=["prev_max_scan"])
            data_ip = data_ip.copy()
            data_ip["scan_date"] = pd.to_datetime(data_ip["scan_date"])

            # lignes non corrigé
            history_uncorected = history_table[history_table["correction"] == 0]

            # lignes corrigé qui ne seront jamais à modifiés
            history_corected = history_table[history_table["correction"] == 1]
            history_corected.loc[:, "correction_delay"] = (history_corected['max_scan_date'] -
                                                           history_corected["min_scan_date"]).dt.days
            # vérifier l'unicité dans data_ip
            data_ip = data_ip.drop_duplicates(subset=colonne_cle + ['scan_date'], keep='first')
            # on merge les données pour touver les couples (ip_id, plugin_id) rescanné,
            # et les nouveaux les couples (ip_id, plugin_id) à ajouter
            merged_df = history_uncorected.merge(data_ip,
                                                 on=colonne_cle,
                                                 how='outer',
                                                 suffixes=('', '_right'))
            # selection des lignes à mettre à jour
            maj_merged = merged_df[merged_df["max_scan_date"].notna()]

            # lignes d'historique non corrigé, ou le plugin n'a pas été scanner depuis la derniere maj
            # il faut pour ces lignes determiner combien de fois l'ip a été scanné depuis la dernière fois que
            # la vulnérabilité a été remonté
            history_uncor_to_ver = maj_merged[~maj_merged["scan_date"].notna()][history_table.columns]

            # jointure sur l'historique des données non corrigé et non remonté depuis la dernière maj,
            # avec l'ensemble des résultats de scan
            pc_df["scan_date"] = pd.to_datetime(pc_df["scan_date"])
            merged_to_ver = pd.merge(pc_df,
                                     history_uncor_to_ver,
                                     on='ip_id',
                                     how='right')

            # Filtre des dates dans merged_to_ver où scan_date est supérieur à max_scan_date
            filtered_df = merged_to_ver[merged_to_ver['scan_date'] > merged_to_ver['max_scan_date']]

            # Compteur des dates uniques par ip_id
            count = filtered_df.groupby('ip_id')['scan_date'].nunique()
            # ajout de 'count' à df2 à partir des résultats de précdent en remplaçant les na par des 0
            history_uncor_to_ver['count'] = history_uncor_to_ver['ip_id'].map(count).fillna(0).astype(int)
            # corrected si au moins 2 scan depuis max_scan_date
            history_uncor_to_ver["correction"] = [True if count >= 2 else False for count in history_uncor_to_ver['count']]

            # On redifini les corrections delay
            max_scan_date = filtered_df.groupby('ip_id')['scan_date'].max()
            history_uncor_to_ver['date_to_add'] = history_uncor_to_ver['ip_id'].map(max_scan_date)
            history_uncor_to_ver['date_to_add'] = pd.to_datetime(history_uncor_to_ver['date_to_add'])

            history_uncor_0 = history_uncor_to_ver[history_uncor_to_ver["count"] == 0]
            history_uncor_1 = history_uncor_to_ver[history_uncor_to_ver["count"] == 1]
            history_uncor_2 = history_uncor_to_ver[history_uncor_to_ver["count"] >= 2]

            history_uncor_1.loc[:, "correction_delay"] = (history_uncor_1['date_to_add'] -
                                                          history_uncor_1["min_scan_date"]).dt.days
            history_uncor_2.loc[:, "correction_delay"] = (history_uncor_2['max_scan_date'] -
                                                          history_uncor_2["min_scan_date"]).dt.days

            history_uncor_to_ver = pd.concat([history_uncor_0,
                                              history_uncor_1,
                                              history_uncor_2])
            # On commence à reconstruire l'historique couples (ip_id, plugin_id) corrigé + mise a jour statut corrected
            history_table_new = pd.concat([history_corected,
                                           history_uncor_to_ver],
                                          ignore_index=True).reset_index()

            # lignes d'historique non corrigé, ou le plugin a été scanner depuis la derniere maj
            maj_merged = maj_merged[maj_merged["scan_date"].notna()]

            # Mise à jour des lignes
            maj_merged["correction_delay"] += (maj_merged["scan_date"] -
                                               maj_merged["max_scan_date"]).dt.days
            maj_merged["max_scan_date"] = maj_merged["scan_date"]
            maj_merged = maj_merged[history_table.columns]

            # On ajoute les lignes mises à jour à l'historique
            history_table_new = pd.concat([history_table_new,
                                           maj_merged],
                                          ignore_index=True).reset_index()

            # Nouveaux couples (ip_id, plugin_id) à ajouter à l'historique
            new_row = merged_df[~merged_df["max_scan_date"].notna()]
            new_row = new_row[new_row["scan_date"].notna()]

            # modification pour intégrer les nouveaux couples
            new_row["max_scan_date"] = new_row["min_scan_date"] = new_row["scan_date"]
            new_row["correction"] = False
            new_row["correction_delay"] = 0
            new_row = new_row[history_table.columns]
            # ajout des nouvelles lignes à l'historique
            history_table_new = pd.concat([history_table_new[history_table.columns],
                                           new_row], ignore_index=True).reset_index()

            # on reformate la table
            history_table_new = history_table_new[history_table.columns]
            history_table_new.drop("id", axis=1, errors="ignore")
            history_table_new["timestamp"] = date.today()
            return history_table_new

        if self.table_pc_history is None or self.table_pc_history.empty:
            data_pc_2 = data_pc.copy()
            pc_history = data_pc_2.groupby(
                    entities_list + ["ip_id", "plugin_id", "severity",
                                     "orange_cyber_rating_family_id",
                                     "orange_cyber_rating_category_id"]
                    ).apply(create_pc_history_table).reset_index()
            pc_history["privacy"] = self.privacy
            pc_history["timestamp"] = date.today()
        else:
            pc_history = maj_pc_history_table(self.table_pc_history, pc_df, self.privacy)
            pc_history = pc_history.drop_duplicates()

        last_scan_ip = ref_rt[["id", "last_scanned_at"]].sort_values(by="last_scanned_at", ascending=False).drop_duplicates(subset=["id"], keep="first")
        df = pc_history.merge(last_scan_ip, left_on="ip_id", right_on="id")
        df = df.merge(self.severity[["severity", "patching_cadence_days"]], on="severity")
        df = df[entities_list + ["ip_id", "plugin_id", "severity",
                                 "orange_cyber_rating_family_id",
                                 "orange_cyber_rating_category_id",
                                 "min_scan_date", "max_scan_date",
                                 "last_scanned_at", "correction",
                                 "correction_delay", "patching_cadence_days", "privacy"]]

        if entities_level == "entities":
            self.pc_df = df
            self.pc_history = pc_history
        else:
            self.pc_df_asset = df
            self.pc_history = pd.concat([self.pc_history, pc_history])
            # On retire les lignes pour lesquelles les Ip on été déplacées
            self.pc_history = self.pc_history.dropna(subset=["subsidiary", "cluster", "division", "asset"], how="all")

    @staticmethod
    def assign_pc_weight(corrected, cor_del, pc_days, severity, max_scan_date, pc_penalty_lose, pc_penalty_gain):
        """
        Calcule le poids de pénalité ou de gain associé à la correction d'une vulnérabilité,
        selon le délai de correction, la sévérité et la date de scan.

        Args:
            corrected (bool): Statut de correction (True si corrigé).
            cor_del (int): Délai de correction (en jours).
            pc_days (int): Délais de correction accordé avant pénalité (en jours).
            severity (int): Niveau de sévérité (1 à 4).
            max_scan_date (datetime): Date du dernier scan.
            pc_penalty_lose (list): Liste des pénalités par sévérité en cas de retard.
            pc_penalty_gain (list): Liste des gains par sévérité en cas de correction dans les temps.

        Returns:
            float: Score de pénalité ou de gain.
        """
        penalty = 0
        if corrected is False and cor_del <= pc_days:
            penalty = 0
        elif corrected and cor_del <= pc_days:
            penalty = pc_penalty_gain[severity - 1]
        elif corrected and cor_del > pc_days:
            delta_months = (datetime.now().year - max_scan_date.year) * 12 + datetime.now().month - max_scan_date.month
            coef_deg = max(0, 1 - (delta_months // 3) * 0.25)
            penalty = -(cor_del // pc_days) * pc_penalty_lose[severity - 1] * coef_deg
        elif corrected is False and cor_del > pc_days:
            penalty = -(cor_del // pc_days) * pc_penalty_lose[severity - 1]
        return penalty

    @staticmethod
    def sum_within_range(x):
        """
        Calcule la note de patching cadence, moyenne des pénalités de l'entité
        centrée sur 100, bornée entre 0 et 100.

        Args:
            x (pd.Series): Série de valeurs numériques.

        Returns:
            float: Somme moyenne bornée entre 0 et 100.
        """
        sum_result = min(max(100 + x.mean(), 0), 100)
        return sum_result

    def get_pc_issued_ips(self, df, entities_level):
        """
        Ajoute la colonne 'issued_ips' correspondant au nombre d'IP scannées
        par entité ou asset dans un DataFrame.

        Args:
            df (DataFrame): DataFrame principal auquel ajouter les IP émises.
            entities_level (str): Niveau d'entité, "entities" ou "asset".

        Returns:
            DataFrame: DataFrame enrichi avec la colonne 'issued_ips'.
        """
        if entities_level == "entities":
            level = "subsidiary"
            pc_df = self.pc_df
        else:
            pc_df = self.pc_df_asset
            level = "asset"

        df_issued_ips = pc_df.groupby([level])["ip_id"].nunique().reset_index()
        df_issued_ips.rename(columns={"ip_id": "issued_ips"}, inplace=True)

        df = df.merge(
            df_issued_ips[[level, "issued_ips"]],
            on=[level],
            how="left")
        return df

    def plugin_exclusion(self, df):
        df = df.merge(
            self.plugin_excluded_from_pc,
            on=["ip_id", "plugin_id"],
            how="left",
            indicator=True
        ).query('_merge == "left_only"').drop(columns="_merge")
        return df

    def calculate_patching_cadence(self, entities_level):
        """
        Calcule le score de patching cadence pour chaque entité ou asset,
        en évaluant les pénalités et les gains liés aux délais de correction.

        Args:
            entities_level (str): Niveau d'entité ("entities" ou "asset").

        Returns:
            DataFrame: Score de patching cadence par entité ou asset.
        """
        self.prepare_patching_cadence(entities_level)
        delta_year = datetime.now() - timedelta(days=365)
        if entities_level == "entities":
            # # Filtre sur les familles avec un poids à 0
            # self.pc_df = self.pc_df[~self.pc_df["orange_cyber_rating_family_id"].isin(self.family.loc[self.family["weight"] == 0, "id"])]
            # Filter plugins to exclude from the rating
            self.pc_df = self.plugin_exclusion(self.pc_df)
            # # Filtre des catégories pour les éventueles changement de cat
            self.pc_df = self.pc_df.merge(
                        self.ref_rt[["id", "category"]],
                        left_on="ip_id",
                        right_on="id",
                        how="left")
            self.pc_df = self.pc_df[self.pc_df["category"].isin(self.ip_type)].copy()
            self.pc_df = self.pc_df.drop(["category", "id"], axis=1, errors="ignore")
            entities_list = self.list_level
            self.pc_df["max_scan_date"] = pd.to_datetime(self.pc_df["max_scan_date"])
            # Sous-ensemble où correction == False
            self.pc_df["correction"] = self.pc_df["correction"].astype(bool)
            df_correction_false = self.pc_df[~self.pc_df["correction"]]
            # Sous-ensemble où correction == True et max_scan_date >= delta_year
            df_correction_true = self.pc_df[(self.pc_df["correction"])]
            df_correction_true = df_correction_true[(df_correction_true["max_scan_date"] >= delta_year)]
            # Concaténer les deux sous-ensembles
            pc_df = pd.concat([df_correction_false, df_correction_true])
        else:
            entities_list = ["asset"]
            # # Filtre sur les familles avec un poids à 0
            # self.pc_df_asset = self.pc_df_asset[~self.pc_df_asset["orange_cyber_rating_family_id"].isin(self.family.loc[self.family["weight"] == 0, "id"])]
            # Filter plugins to exclude from the rating
            self.pc_df_asset = self.plugin_exclusion(self.pc_df_asset)
            # Filtre des catégories d'ip pour les éventueles changement de cat
            self.pc_df_asset = self.pc_df_asset.merge(
                        self.ref_rt[["id", "category"]],
                        left_on="ip_id",
                        right_on="id",
                        how="left")
            self.pc_df_asset = self.pc_df_asset[self.pc_df_asset["category"].isin(self.ip_type)].copy()
            self.pc_df_asset = self.pc_df_asset.drop(["category", "id"], axis=1, errors="ignore")
            self.pc_df_asset.loc[:, "max_scan_date"] = pd.to_datetime(self.pc_df_asset["max_scan_date"])
            self.pc_df_asset["correction"] = self.pc_df_asset["correction"].astype(bool)
            df_correction_false = self.pc_df_asset[~self.pc_df_asset["correction"]]
            # Sous-ensemble où correction == True et max_scan_date >= delta_year
            df_correction_true = self.pc_df_asset[(self.pc_df_asset["correction"])]
            df_correction_true = df_correction_true[(df_correction_true["max_scan_date"] >= delta_year)]
            # Concaténer les deux sous-ensembles
            pc_df = pd.concat([df_correction_false, df_correction_true])

        if not pc_df.empty:
            pc_df.loc[:, "penalty"] = pc_df.apply(
                lambda row: self.assign_pc_weight(
                    row["correction"],
                    row["correction_delay"],
                    row["patching_cadence_days"],
                    row["severity"],
                    row["max_scan_date"],
                    pc_penalty_lose=self.pc_penalty_lose,
                    pc_penalty_gain=self.pc_penalty_gain,
                ),
                axis=1)
            list_column = entities_list + ["penalty", "orange_cyber_rating_family_id",
                                           "orange_cyber_rating_category_id"]
            df = pc_df[list_column].groupby(entities_list).apply(lambda x: pd.Series(
                {"family_rating": self.sum_within_range(x["penalty"])})).reset_index()
        else:
            df = pd.DataFrame(columns=entities_list + ["family_rating"])
        return df

    def add_patching_cadence(self, df, entities_level):
        """
        Ajoute les informations de patching cadence (score, famille 7)
        au data frame contenant les scores des familles.

        Args:
            df (DataFrame): DataFrame à enrichir.
            entities_level (str): Niveau d'entité ("entities" ou "asset").

        Returns:
            DataFrame: DataFrame enrichi avec les données de patching cadence.
        """
        list_gb = ["privacy", "level", "group"] + self.list_level + ["asset"]
        list_cols = list_gb + ["parc_size"]

        if entities_level == "entities":
            entities_list = self.list_level
        else:
            entities_list = ["asset"]
        if not df.empty:
            gb = df[list_cols].groupby(list_gb, dropna=False).agg({"parc_size": "first"}).reset_index()
            pc = self.calculate_patching_cadence(entities_level)
            merged_df = gb.merge(pc, on=entities_list, how="inner")
            merged_df = self.get_pc_issued_ips(merged_df, entities_level)
            pc_family = self.family[self.family["name"] == self.pc_fam].id.values[0]
            merged_df = merged_df.assign(family=pc_family)
            # df = pd.concat([df, merged_df], ignore_index=True)
            df_list = [df, merged_df]
            all_columns = pd.Index([])
            for df in df_list:
                all_columns = all_columns.union(df.columns)
            df_non_empty = [df for df in df_list if not df.isna().all().all()]
            if df_non_empty:
                df = pd.concat(df_non_empty, ignore_index=True)
                df = df.reindex(columns=all_columns)
            else:
                df = pd.DataFrame()
                df = df.reindex(columns=all_columns)
        else:
            df = pd.DataFrame(columns=[
                'privacy', 'level', 'group', 'division', 'cluster', 'subsidiary',
                'asset', 'family', 'family_rating', 'parc_size', 'issued_ips'])

        return df

    def join_all_level(self, plugin_level, df_sub, df_clust, df_div, df_group, df_asset):
        """
        Concatène les données à tous les niveaux hiérarchiques pour un niveau de plugin donné.

        Args:
            plugin_level (str): Niveau du plugin ("category", "family", ou "total").
            df_sub (DataFrame): Données au niveau subsidiary.
            df_clust (DataFrame): Données au niveau cluster.
            df_div (DataFrame): Données au niveau division.
            df_group (DataFrame): Données au niveau group.
            df_asset (DataFrame): Données au niveau asset.

        Returns:
            DataFrame: Données concaténées à tous les niveaux, avec colonnes harmonisées.
        """
        df_list = [df_sub, df_clust, df_div, df_group, df_asset]
        # Identifier l'ensemble des colonnes présentes dans les DataFrames
        all_columns = pd.Index([])
        for df in df_list:
            all_columns = all_columns.union(df.columns)
        df_non_empty = [df for df in df_list if not df.isna().all().all()]
        if df_non_empty:
            df = pd.concat(df_non_empty, ignore_index=True)
            df = df.reindex(columns=all_columns)
            df.insert(0, "timestamp", date.today())
        else:
            df = pd.DataFrame()
            df = df.reindex(columns=all_columns)

        if plugin_level == "family":
            plugin_list = [plugin_level, plugin_level + "_rating"]
        elif plugin_level == "category":
            plugin_list = ["family", plugin_level, plugin_level + "_rating"]
        else:
            plugin_list = ["total_rating"]

        try:
            df = df[["timestamp", "privacy", "level", "group"] +
                    self.list_level + ["asset"] +
                    plugin_list + ["parc_size", "issued_ips", "parc_size_sum"]]
        except KeyError:
            df = pd.DataFrame(columns=["timestamp", "privacy", "level", "group"] +
                              self.list_level + ["asset"] +
                              plugin_list + ["parc_size", "issued_ips", "parc_size_sum"])
        return df

    def initializing_data(self):
        """
        Initialise toutes les données nécessaires au calcul des indicateurs :
        préparation, création des données IP par entité et asset.
        """
        self.preparing_data()
        self.creating_data_ip("entities")
        self.creating_data_ip("asset")

    def create_data_category(self):
        """
        Orchestre la génèration des notations aux niveau des catégories
        (par entité et asset), puis les agrège à tous les niveaux.
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        # Defensive check: ensure data_ip is available
        if not hasattr(self, 'data_ip') or self.data_ip is None or self.data_ip.empty:
            my_logger.warning("data_ip is empty, creating empty data_category")
            self.data_category = pd.DataFrame(columns=[
                'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                'subsidiary', 'asset', 'family', 'category', 'category_rating',
                'parc_size', 'issued_ips'
            ])
            return

        # Defensive check: ensure data_ip_asset is available
        if not hasattr(self, 'data_ip_asset') or self.data_ip_asset is None:
            my_logger.warning("data_ip_asset is None, setting to empty DataFrame")
            self.data_ip_asset = pd.DataFrame()

        try:
            data_category_sub = self.create_first_level(self.data_ip, "subsidiary")
            data_category_clust = self.calculate_up_entity("cluster", "category", data_category_sub)
            data_category_div = self.calculate_up_entity("division", "category", data_category_clust)
            data_category_group = self.calculate_up_entity("group", "category", data_category_div)

            data_category_asset = self.create_first_level(self.data_ip_asset, "asset")

            data_category = self.join_all_level("category",
                                                data_category_sub,
                                                data_category_clust,
                                                data_category_div,
                                                data_category_group,
                                                data_category_asset)

            # Defensive check: ensure result is not None
            if data_category is None or data_category.empty:
                my_logger.warning("join_all_level returned empty data_category")
                self.data_category = pd.DataFrame(columns=[
                    'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                    'subsidiary', 'asset', 'family', 'category', 'category_rating',
                    'parc_size', 'issued_ips'
                ])
            else:
                self.data_category = data_category
                my_logger.info(f"Successfully created data_category with {len(data_category)} records")

        except Exception as e:
            my_logger.error(f"Error in create_data_category: {e}")
            self.data_category = pd.DataFrame(columns=[
                'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                'subsidiary', 'asset', 'family', 'category', 'category_rating',
                'parc_size', 'issued_ips'
            ])

    def create_data_family(self):
        """
        Orchestre la génèration des notes au niveau des familles,
        (par entité et asset), ajoute le patching cadence,
        puis les agrège à tous les niveaux.
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        # Defensive check: ensure data_category is available
        if not hasattr(self, 'data_category') or self.data_category is None or self.data_category.empty:
            my_logger.warning("data_category is empty, creating empty data_family")
            self.data_family = pd.DataFrame(columns=[
                'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                'subsidiary', 'asset', 'family', 'family_rating', 'parc_size', 'issued_ips'
            ])
            return

        try:
            # Defensive filtering with empty check
            if 'level' in self.data_category.columns:
                data_category_sub = self.data_category[self.data_category.level == "subsidiary"]
                data_category_asset = self.data_category[self.data_category.level == "asset"]
            else:
                my_logger.warning("level column missing in data_category")
                data_category_sub = pd.DataFrame()
                data_category_asset = pd.DataFrame()

            data_family_sub = self.calculate_up_plugin_level("family", data_category_sub, "subsidiary")
            data_family_sub = self.add_patching_cadence(data_family_sub, "entities")

            data_family_sub = self.remove_entity(data_family_sub, "subsidiary")
            data_family_sub = self.clean_fam(data_family_sub, "subsidiary")
            data_family_clust = self.calculate_up_entity("cluster", "family", data_family_sub)
            data_family_div = self.calculate_up_entity("division", "family", data_family_clust)
            data_family_group = self.calculate_up_entity("group", "family", data_family_div)

            data_family_asset = self.calculate_up_plugin_level("family", data_category_asset, "asset")
            data_family_asset = self.add_patching_cadence(data_family_asset, "asset")
            data_family_asset = self.clean_fam(data_family_asset, "asset")

            data_family = self.join_all_level("family",
                                              data_family_sub,
                                              data_family_clust,
                                              data_family_div,
                                              data_family_group,
                                              data_family_asset)

            # Defensive check: ensure result is not None
            if data_family is None or data_family.empty:
                my_logger.warning("join_all_level returned empty data_family")
                self.data_family = pd.DataFrame(columns=[
                    'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                    'subsidiary', 'asset', 'family', 'family_rating', 'parc_size', 'issued_ips'
                ])
            else:
                self.data_family = data_family
                my_logger.info(f"Successfully created data_family with {len(data_family)} records")

            self.add_pc_to_data_category()

        except Exception as e:
            my_logger.error(f"Error in create_data_family: {e}")
            self.data_family = pd.DataFrame(columns=[
                'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                'subsidiary', 'asset', 'family', 'family_rating', 'parc_size', 'issued_ips'
            ])

    def add_pc_to_data_category(self):
        """
        Ajoute à `data_category` les scores calculés à partir de la famille patching cadence.
        """
        pc_family = self.family[self.family["name"] == self.pc_fam].id.values[0]
        pc_category = self.category[self.category["name"] == self.pc_fam].id.values[0]

        data_family_pc = self.data_family[self.data_family["family"] == pc_family]
        data_category_pc = data_family_pc.assign(category=pc_category)
        data_category_pc.rename(columns={"family_rating": "category_rating"}, inplace=True)

        self.data_category = pd.concat([self.data_category, data_category_pc])

    def clean_fam(self, df, level):
        """
        Filtre le DataFrame df en associant aux subsidiaries/assets dont toutes
        les familles ont des poids nuls une ligne 'Patching Cadence' avec family_rating = -1.
        Si cette ligne n'existe pas déjà, elle est ajoutée à partir d'une ligne existante du même groupe.

        Parameters
        ----------
        df : pd.DataFrame
            DataFrame contenant les colonnes 'subsidiary' et 'family'.
            Représente les relations entre les subsidiaries/assets et les familles.
        level : str
            'subsidiary' ou 'asset'

        Returns
        -------
        pd.DataFrame
            DataFrame filtré, avec lignes 'Patching Cadence' ajoutées si besoin.
        """
        # Familles considérées comme "nulles"
        families_to_remove = self.family.loc[
            (self.family['weight'] == 0) | (self.family['name'] == 'Patching Cadence'),
            'id'
        ].unique()

        # Identifier les subsidiaries/assets à remplacer
        group_key = level
        subsidiaries_to_replace = (
            df.groupby(group_key)['family']
            .apply(lambda x: set(x).issubset(set(families_to_remove)))
        )

        subs_to_replace = subsidiaries_to_replace[subsidiaries_to_replace].index.tolist()

        # Famille Patching Cadence
        patching_cadence_id = self.family[self.family['name'] == 'Patching Cadence']['id'].iloc[0]

        for sub in subs_to_replace:
            sub_mask = df[group_key] == sub
            sub_df = df[sub_mask]

            # Vérifie si 'Patching Cadence' est déjà présente
            if patching_cadence_id not in sub_df['family'].values:
                # On duplique une ligne existante (la première) et on modifie les champs
                new_row = pd.Series({col: sub_df.iloc[0].get(col) for col in df.columns})
                new_row['family'] = patching_cadence_id
                new_row['family_rating'] = -1
                df = pd.concat([df, new_row.to_frame().T], ignore_index=True)
                if level == "subsidiary":
                    df[f"remove_{level}"] = df[f"remove_{level}"].astype(bool)
        return df

    def create_data_total(self):
        """
        Calcule les scores totaux à partir des données famille, puis les agrège
        à tous les niveaux (de asset à group).
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        # Defensive check: ensure data_family is available
        if not hasattr(self, 'data_family') or self.data_family is None or self.data_family.empty:
            my_logger.warning("data_family is empty, creating empty data_total")
            self.data_total = pd.DataFrame(columns=[
                'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                'subsidiary', 'asset', 'total_rating', 'parc_size', 'issued_ips'
            ])
            return

        try:
            # Defensive filtering with empty check
            if 'level' in self.data_family.columns:
                data_family_sub = self.data_family[self.data_family.level == "subsidiary"]
                data_family_asset = self.data_family[self.data_family.level == "asset"]
            else:
                my_logger.warning("level column missing in data_family")
                data_family_sub = pd.DataFrame()
                data_family_asset = pd.DataFrame()

            data_total_sub = self.calculate_up_plugin_level("total", data_family_sub, "subsidiary")
            data_total_sub = self.remove_entity(data_total_sub, "subsidiary")
            data_total_clust = self.calculate_up_entity("cluster", "total", data_total_sub)
            data_total_div = self.calculate_up_entity("division", "total", data_total_clust)
            data_total_group = self.calculate_up_entity("group", "total", data_total_div)

            data_total_asset = self.calculate_up_plugin_level("total", data_family_asset, "asset")

            data_total = self.join_all_level("total",
                                             data_total_sub,
                                             data_total_clust,
                                             data_total_div,
                                             data_total_group,
                                             data_total_asset)

            # Defensive check: ensure result is not None
            if data_total is None or data_total.empty:
                my_logger.warning("join_all_level returned empty data_total")
                self.data_total = pd.DataFrame(columns=[
                    'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                    'subsidiary', 'asset', 'total_rating', 'parc_size', 'issued_ips'
                ])
            else:
                self.data_total = data_total
                my_logger.info(f"Successfully created data_total with {len(data_total)} records")

        except Exception as e:
            my_logger.error(f"Error in create_data_total: {e}")
            self.data_total = pd.DataFrame(columns=[
                'timestamp', 'privacy', 'level', 'group', 'division', 'cluster',
                'subsidiary', 'asset', 'total_rating', 'parc_size', 'issued_ips'
            ])

    @staticmethod
    def log_wa(rating, size_parc):
        """
        Calcule une moyenne pondérée logarithmique des ratings,
        pondérée par le log de la taille du parc.

        Args:
            rating (pd.Series): Séries des scores à moyenner.
            size_parc (pd.Series): Taille du parc, utilisée comme poids.

        Returns:
            float: Moyenne pondérée logarithmique.
        """
        valid_mask = rating != -1
        if not valid_mask.any():
            return -1

        rating = rating[valid_mask]
        size_parc = size_parc[valid_mask]

        weights = (size_parc + 1).apply(math.log)
        return np.average(rating, weights=weights)

    def severity_pc_mean_by_level(self, df, plugin_level, entities_level):
        """
        Calcule le délai moyen de correction des vulnérabilités par niveau de sévérité,
        en fonction d'un niveau de granularité (asset ou organisation) et d'un niveau de classification des plugins (famille ou catégorie).

        Cette méthode agrège les délais de correction pour les vulnérabilités corrigées ou toujours ouvertes
        dans l'année écoulée, et les regroupe par sévérité (low, medium, high, critical),
        selon le niveau d'entité sélectionné.

        Args:
            df (DataFrame) : DataFrame contenant les informations de correction, généralement `self.pc_df` ou `self.pc_df_asset`.
            plugin_level (str) : Niveau de classification des plugins, soit `"family"` soit `"category"`.
            entities_level (str) : Niveau d'agrégation souhaité : `"entities"` (groupe/cluster/etc.) ou `"asset"` (machine spécifique).

        Returns:
            DataFrame : Délai moyen de correction par sévérité, classé par entité et type de plugin.
                        Les colonnes retournées suivent le format :
                        - `low_pc_mean_{plugin_level}`
                        - `medium_pc_mean_{plugin_level}`
                        - `high_pc_mean_{plugin_level}`
                        - `critical_pc_mean_{plugin_level}`
        """
        delta_year = datetime.now() - timedelta(days=365)
        if entities_level == "entities":
            entities_list = ["group", "division",
                             "cluster", "subsidiary"]
        else:
            entities_list = ["asset"]
        pc_data = pd.DataFrame()
        if plugin_level == "category":
            plugin_col = ["orange_cyber_rating_family_id",
                          "orange_cyber_rating_category_id"]
        else:
            plugin_col = ["orange_cyber_rating_family_id"]

        if df is not None:
            df = df.assign(group="Orange")
            df["max_scan_date"] = pd.to_datetime(df["max_scan_date"])
            # Sous-ensemble où correction == False
            df["correction"] = df["correction"].astype(bool)
            df_correction_false = df[~df["correction"]]
            # Sous-ensemble où correction == True et max_scan_date >= delta_year
            df_correction_true = df[(df["correction"])]
            df_correction_true = df_correction_true[(df_correction_true["max_scan_date"] >= delta_year)]
            # Concaténer les deux sous-ensembles
            df = pd.concat([df_correction_false, df_correction_true])
            for level in entities_list:
                pc_pivot = pd.pivot_table(
                    df,
                    index=entities_list[:entities_list.index(level) + 1] + plugin_col,
                    columns="severity",
                    values="correction_delay",
                    aggfunc="mean",
                    fill_value=0,
                ).reset_index()
                # on conserve l'information du level dans la colonne level
                pc_pivot = pc_pivot.assign(level=level)
                # on concatène les différents level
                pc_data = pd.concat([pc_data, pc_pivot])
            # En sortie de la boucle on a pc_data qui contient les correction_delay moyen
            # par sévérité en fonction du level pour les categoy ou family en fonction de plugin_level
            list_severity = self.severity["severity"].to_list()
            for severity_level in list_severity:
                if (severity_level not in pc_data.columns and severity_level != 0):
                    pc_data[severity_level] = 0

            pc_data.rename(columns={1: "low_pc_mean_" + plugin_level,
                                    2: "medium_pc_mean_" + plugin_level,
                                    3: "high_pc_mean_" + plugin_level,
                                    4: "critical_pc_mean_" + plugin_level}, inplace=True)

        if (df is None or pc_data.empty):
            pc_data = pd.DataFrame(columns=["group", "orange_cyber_rating_family_id",
                                            f"low_pc_mean_{plugin_level}",
                                            f"medium_pc_mean_{plugin_level}",
                                            f"high_pc_mean_{plugin_level}",
                                            f"critical_pc_mean_{plugin_level}",
                                            "level"] + entities_list)
        return pc_data

    def create_severity_pc_mean_table(self):
        """
        Génère une table combinée des délais moyens de correction par sévérité,
        en croisant les deux niveaux de classification des plugins (famille et catégorie),
        pour les entités et les assets.

        Cette méthode appelle `severity_pc_mean_by_level()` pour chaque combinaison :
        - (entities, category)
        - (entities, family)
        - (asset, category)
        - (asset, family)

        Les résultats sont concaténés afin de construire une table complète contenant :
        - les délais moyens de correction par sévérité
        - les identifiants de classification (family/category)
        - les informations de contexte (division, cluster, asset, etc.)

        La table finale est stockée dans l'attribut `self.pc_db_sm`.

        Structure attendue de la table de sortie :
            - `low_pc_mean_{category/family}`
            - `medium_pc_mean_{category/family}`
            - `high_pc_mean_{category/family}`
            - `critical_pc_mean_{category/family}`
            - `asset`, `division`, `cluster`, `subsidiary`, `orange_cyber_rating_{family/category}_id`, etc.

        Remplit également la colonne `privacy` selon la configuration de l'objet (`self.privacy`).
        """
        pc_df_cat = self.severity_pc_mean_by_level(self.pc_df, "category", "entities")
        pc_df_fam = self.severity_pc_mean_by_level(self.pc_df, "family", "entities")

        cols = ["critical_pc_mean_family", "low_pc_mean_family", "high_pc_mean_family",
                "medium_pc_mean_family", "division", "cluster", "subsidiary", "level",
                "orange_cyber_rating_family_id"]
        cat_fam = pc_df_cat.merge(pc_df_fam[cols],
                                  how="inner",
                                  on=["division", "cluster",
                                      "subsidiary", "level",
                                      "orange_cyber_rating_family_id"])
        if (self.pc_df_asset is not None and not self.pc_df_asset.empty):
            pc_df_cat2 = self.severity_pc_mean_by_level(self.pc_df_asset, "category", "asset")
            pc_df_fam2 = self.severity_pc_mean_by_level(self.pc_df_asset, "family", "asset")

            cols2 = ["critical_pc_mean_family", "low_pc_mean_family", "high_pc_mean_family",
                     "medium_pc_mean_family", "asset", "level", "orange_cyber_rating_family_id"]

            cat_fam2 = pc_df_cat2.merge(pc_df_fam2[cols2],
                                        how="inner",
                                        on=["asset", "level",
                                            "orange_cyber_rating_family_id"])
        else:
            cat_fam2 = pd.DataFrame(columns=['asset', 'orange_cyber_rating_category_id',
                                             'low_pc_mean_category', 'medium_pc_mean_category',
                                             'high_pc_mean_category', 'critical_pc_mean_category',
                                             'level', 'critical_pc_mean_family', 'low_pc_mean_family',
                                             'high_pc_mean_family', 'medium_pc_mean_family',
                                             'orange_cyber_rating_family_id'])
        cat_fam.reset_index(drop=True)
        cat_fam2.reset_index(drop=True)
        if cat_fam.empty and cat_fam2.empty:
            df = pd.DataFrame(columns=['asset', 'orange_cyber_rating_category_id',
                                       'low_pc_mean_category', 'medium_pc_mean_category',
                                       'high_pc_mean_category', 'critical_pc_mean_category',
                                       'level', 'critical_pc_mean_family', 'low_pc_mean_family',
                                       'high_pc_mean_family', 'medium_pc_mean_family',
                                       'orange_cyber_rating_family_id', "group", "division",
                                       "cluster", "subsidiary"
                                       ])
        else:
            df_list = [cat_fam, cat_fam2]
            all_columns = pd.Index([])
            column_types = {}
            for df in df_list:
                all_columns = all_columns.union(df.columns)
                column_types.update(df.dtypes.to_dict())
            df_non_empty = [df for df in df_list if not df.isna().all().all()]
            if df_non_empty:
                df_concat = pd.concat(df_non_empty, ignore_index=True)
                df = df_concat.reindex(columns=all_columns)
                df["privacy"] = self.privacy
            else:
                df = pd.DataFrame()
                df = df.reindex(columns=all_columns)
        df.drop("group", axis=1, inplace=True)
        self.pc_db_sm = df

    @staticmethod
    def compute_fix_proportions(df, group_col):
        # Proportions par sévérité
        severity_map = {'medium': 2, 'high': 3, 'critical': 4}
        reverse_severity_map = {v: k for k, v in severity_map.items()}
        severity_df = df[df['considered']].groupby([group_col, 'severity']).agg(
            total=('considered', 'count'),
            on_time=('on_time_fix', 'sum')
        ).reset_index()

        severity_df['prop_on_time'] = round((severity_df['on_time'] / severity_df['total']) * 100, 2)

        # Pivot pour colonnes par sévérité
        pivot_prop = severity_df.pivot(index=group_col, columns='severity', values='prop_on_time')
        pivot_prop = pivot_prop.rename(columns={
            lvl: f'prop_{reverse_severity_map[lvl]}_on_time' for lvl in pivot_prop.columns
        })

        # Pivot pour les volumes (nb de vulnérabilités considérées par sévérité)
        pivot_count = severity_df.pivot(index=group_col, columns='severity', values='total')
        pivot_count = pivot_count.rename(columns={
            lvl: f'count_{reverse_severity_map[lvl]}' for lvl in pivot_count.columns
        })

        #  Fusion des deux pivots
        pivot = pivot_prop.join(pivot_count).reset_index()

        # Proportion globale (sans distinction de sévérité)
        global_df = df[df['considered']].groupby(group_col).agg(
            total=('considered', 'count'),
            on_time=('on_time_fix', 'sum')
        ).reset_index()
        global_df['proportion_fixed_on_time'] = round((global_df['on_time'] / global_df['total']) * 100, 2)
        vuln_total_df = df.groupby(group_col).size().reset_index(name='vuln_total')
        vuln_count_df = df[df['considered']].groupby(group_col).size().reset_index(name='vuln_count')
        # Merge final
        result = pivot.merge(global_df[[group_col, 'proportion_fixed_on_time']], on=group_col)
        result = result.merge(vuln_count_df, on=group_col, how='left')
        result = result.merge(vuln_total_df, on=group_col, how='left')
        return result

    def create_correction_rate(self):
        pc_rate = self.pc_history[self.pc_history["severity"] != 1].copy()
        pc_rate = pc_rate.merge(self.severity[["severity", "patching_cadence_days"]], on="severity")
        pc_rate['correction'] = pc_rate['correction'].astype(bool)
        # Masques logiques
        pc_rate['considered'] = pc_rate['correction'] | (
            (~pc_rate['correction']) & (pc_rate['correction_delay'] > pc_rate['patching_cadence_days'])
        )

        pc_rate['on_time_fix'] = pc_rate['correction'] & (pc_rate['correction_delay'] <= pc_rate['patching_cadence_days'])

        # Liste des niveaux hiérarchiques à traiter
        levels = ['subsidiary', 'cluster', 'division', 'asset', 'group']
        all_results = []

        for level in levels:
            name_col = f"{level}"
            df = pc_rate.copy()
            if level == "group":
                if not df.empty:
                    df = pc_rate[pc_rate["subsidiary"].notna()].copy()
                    df.loc[:, "group_name"] = "Orange"
                    df.loc[:, "group"] = "Orange"

            if level == "cluster":
                df = pc_rate[pc_rate["cluster"] < 1000].copy()
            # Appliquer la fonction précédente
            if not df.empty:
                level_df = self.compute_fix_proportions(df, group_col=level)

                # Ajout du niveau pour trace
                level_df['level'] = level
                level_df['privacy'] = self.privacy

                # Harmonisation du nom d'entité (pour facilité de concaténation/affichage)
                level_df['entity_id'] = level_df[level]
                # level_df['entity_name'] = level_df[name_col]

                # On sélectionne les colonnes utiles
                final_cols = [col for col in [
                             'level', 'entity_id',
                             *[c for c in level_df.columns if c.startswith('prop_')],
                             *[c for c in level_df.columns if c.startswith('count_')],
                             'proportion_fixed_on_time', 'vuln_count', 'vuln_total'
                             ] if col in level_df.columns]

                all_results.append(level_df[final_cols])
            self.pc_rate = pd.concat(all_results, ignore_index=True) if all_results else pd.DataFrame()


class GeneralConstruction:
    """
    Classe utilitaire pour la génération, la transformation et l'exportation
    de jeux de données liés à des entités (subsidiary, cluster, division, asset, group)
    et à leur notation.

    Attributs:
        asset (pd.DataFrame): Table des asset.
        subsidiary (pd.DataFrame): Table des filiales.
        cluster (pd.DataFrame): Table des clusters.
        division (pd.DataFrame): Table des divisions.
        rating_letters (pd.DataFrame): Table de correspondance des notes.
        family (pd.DataFrame): Table des familles de plugins.
        category (pd.DataFrame): Table des catégories de plugins.
        list_level (List[str]): Liste des niveaux hiérarchiques des entités.
    """
    def __init__(self, di):
        """
        Initialise l'objet avec les différentes sources de données.

        Args:
            di: Objet contenant les attributs `asset`, `subsidiary`, `cluster`, `division`,
                `rating_letters`, `family`, `category`.
                di est une instance de la classe DataImport
        """
        self.asset = di.asset
        self.subsidiary = di.subsidiary
        self.cluster = di.cluster
        self.division = di.division
        self.rating_letters = di.rating_letters
        self.family = di.family
        self.category = di.category
        self.list_level = ["division", "cluster", "subsidiary"]

    def _prepare_reference_df(self, source_df, source_cols, target_cols, entity_type):
        """
        Helper method to safely prepare reference DataFrames for merging.

        Args:
            source_df (pd.DataFrame): Source DataFrame to prepare
            source_cols (list): List of source column names to select
            target_cols (list): List of target column names to rename to
            entity_type (str): Type of entity for logging purposes

        Returns:
            pd.DataFrame: Prepared DataFrame or empty DataFrame if source is invalid
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        if source_df is None or source_df.empty:
            my_logger.warning(f"{entity_type} DataFrame is empty, skipping {entity_type} data")
            return pd.DataFrame()

        # Check if required columns exist
        missing_cols = [col for col in source_cols if col not in source_df.columns]
        if missing_cols:
            my_logger.warning(f"Missing columns in {entity_type} DataFrame: {missing_cols}, skipping {entity_type} data")
            return pd.DataFrame()

        try:
            result_df = source_df[source_cols].copy()
            result_df.columns = target_cols
            return result_df
        except Exception as e:
            my_logger.error(f"Error preparing {entity_type} reference DataFrame: {e}")
            return pd.DataFrame()

    def create_data_today_csv(self, df):
        """
        Crée un CSV avec les notations agrégées du jour, tous niveaux confondus.

        Args:
            df (pd.DataFrame): Données d'entrée contenant les notations par niveau et confidentialité.

        Returns:
            pd.DataFrame: DataFrame finale avec les notations publiques/privées et totales.
        """
        # Defensive check: ensure df is not empty and has required columns
        if df.empty or 'level' not in df.columns:
            my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")
            my_logger.warning("Input DataFrame is empty or missing 'level' column in create_data_today_csv")
            # Return empty DataFrame with expected structure
            return pd.DataFrame(columns=["timestamp", "level", "name", "total_rating", "public_rating", "private_rating"])

        subsidiary = self.subsidiary[["id", "long_name"]]
        subsidiary.columns = ["subsidiary", "name"]
        cluster = self.cluster[["id", "name"]]
        cluster.columns = ["cluster", "name"]
        division = self.division[["id", "long_name"]]
        division.columns = ["division", "name"]
        asset = self.asset[["id", "name"]]
        asset.columns = ["asset", "name"]

        # Fix: Use df["level"] instead of df.level to access column and safe merge operations
        data_sub = safe_merge_with_type_conversion(df[df["level"] == "subsidiary"], subsidiary, on="subsidiary", how="left") if "subsidiary" in df.columns else pd.DataFrame()
        data_clust = safe_merge_with_type_conversion(df[df["level"] == "cluster"], cluster, on="cluster", how="left") if "cluster" in df.columns else pd.DataFrame()
        data_div = safe_merge_with_type_conversion(df[df["level"] == "division"], division, on="division", how="left") if "division" in df.columns else pd.DataFrame()
        data_asset = safe_merge_with_type_conversion(df[df["level"] == "asset"], asset, on="asset", how="left") if "asset" in df.columns else pd.DataFrame()
        data_group = df[df["level"] == "group"]
        data_group = data_group.assign(name="Orange")

        # Filter out empty DataFrames before concatenation
        dfs_to_concat = [df for df in [data_sub, data_clust, data_div, data_asset, data_group] if not df.empty]

        if not dfs_to_concat:
            my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")
            my_logger.warning("No data available for any level in create_data_today_csv")
            return pd.DataFrame(columns=["timestamp", "level", "name", "total_rating", "public_rating", "private_rating"])

        df = pd.concat(dfs_to_concat, ignore_index=True)

        # Defensive check: ensure required columns exist
        required_cols = ["timestamp", "level", "name", "total_rating", "privacy"]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")
            my_logger.warning(f"Missing required columns in create_data_today_csv: {missing_cols}")
            return pd.DataFrame(columns=["timestamp", "level", "name", "total_rating", "public_rating", "private_rating"])

        df = df[required_cols]
        df["total_rating"] = [round(i, 2) if pd.notna(i) else 0.0 for i in df["total_rating"]]

        public = df[df["privacy"] == "public"].rename(columns={"total_rating": "public_rating"})
        private = df[df["privacy"] == "private"].rename(columns={"total_rating": "private_rating"})
        df_all = df[df["privacy"] == "all"]

        df = safe_merge_with_type_conversion(
            df_all, public,
            on=["timestamp", "level", "name"],
            how="left")
        df = safe_merge_with_type_conversion(
            df, private,
            on=["timestamp", "level", "name"],
            how="left")

        df = df[["timestamp", "level", "name", "total_rating", "public_rating", "private_rating"]]
        df.to_csv(os.path.join(BASE_DIR, "out", "data_today.csv"))
        return df

    def create_clean_csv(self, df, filename):
        """
        Exporte un CSV nettoyé contenant les noms des entités selon leur niveau.

        Args:
            df (pd.DataFrame): Données à nettoyer.
            filename (str): Nom du fichier de sortie (sans extension).
        """
        subsidiary = self.subsidiary[["id", "long_name"]]
        subsidiary.columns = ["subsidiary", "name"]
        cluster = self.cluster[["id", "name"]]
        cluster.columns = ["cluster", "name"]
        division = self.division[["id", "long_name"]]
        division.columns = ["division", "name"]
        asset = self.asset[["id", "name"]]
        asset.columns = ["asset", "name"]

        data_sub = df[df.level == "subsidiary"].merge(subsidiary, on="subsidiary")
        data_clust = df[df.level == "cluster"].merge(cluster, on="cluster")
        data_div = df[df.level == "division"].merge(division, on="division")
        data_asset = df[df.level == "asset"].merge(asset, on="asset")
        data_group = df[df.level == "group"]
        data_group = data_group.assign(name="Orange")

        df = pd.concat([data_sub, data_clust, data_div, data_asset, data_group])
        df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"))

    def create_clean_csv_pc(self, df, filename):
        """
        Variante de l'export nettoyé, utilisant les noms de colonnes avec suffixe `_id`.
        pour exporter des données dans un csv depuis la bdd.

        Args:
            df (pd.DataFrame): Données à nettoyer.
            filename (str): Nom du fichier de sortie (sans extension).
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        # Defensive check: handle empty DataFrame
        if df is None or df.empty:
            my_logger.warning("Input DataFrame is empty for create_clean_csv_pc, creating empty output file")
            empty_df = pd.DataFrame(columns=["timestamp", "level", "name"])
            empty_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)
            return

        try:
            # Defensive check: ensure required columns exist in input DataFrame
            if "level" not in df.columns:
                my_logger.warning("level column missing in input DataFrame for create_clean_csv_pc")
                empty_df = pd.DataFrame(columns=["timestamp", "level", "name"])
                empty_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)
                return

            # Defensive preparation of reference DataFrames with proper column mapping
            subsidiary = self._prepare_reference_df(self.subsidiary, ["id", "long_name"], ["subsidiary", "name"], "subsidiary")
            cluster = self._prepare_reference_df(self.cluster, ["id", "name"], ["cluster", "name"], "cluster")
            division = self._prepare_reference_df(self.division, ["id", "long_name"], ["division", "name"], "division")
            asset = self._prepare_reference_df(self.asset, ["id", "name"], ["asset", "name"], "asset")

            # Defensive merging with proper column mapping
            data_frames = []

            # Handle subsidiary data
            if not df[df["level"] == "subsidiary"].empty and not subsidiary.empty and "subsidiary" in df.columns:
                data_sub = safe_merge_with_type_conversion(
                    df[df["level"] == "subsidiary"], subsidiary, on="subsidiary", how="left")
                if not data_sub.empty:
                    data_frames.append(data_sub)

            # Handle cluster data
            if not df[df["level"] == "cluster"].empty and not cluster.empty and "cluster" in df.columns:
                data_clust = safe_merge_with_type_conversion(
                    df[df["level"] == "cluster"], cluster, on="cluster", how="left")
                if not data_clust.empty:
                    data_frames.append(data_clust)

            # Handle division data
            if not df[df["level"] == "division"].empty and not division.empty and "division" in df.columns:
                data_div = safe_merge_with_type_conversion(
                    df[df["level"] == "division"], division, on="division", how="left")
                if not data_div.empty:
                    data_frames.append(data_div)

            # Handle asset data
            if not df[df["level"] == "asset"].empty and not asset.empty and "asset" in df.columns:
                data_asset = safe_merge_with_type_conversion(
                    df[df["level"] == "asset"], asset, on="asset", how="left")
                if not data_asset.empty:
                    data_frames.append(data_asset)

            # Handle group data
            if not df[df["level"] == "group"].empty:
                data_group = df[df["level"] == "group"].copy()
                data_group = data_group.assign(name="Orange")
                data_frames.append(data_group)

            # Combine all data frames
            if data_frames:
                result_df = pd.concat(data_frames, ignore_index=True)
            else:
                my_logger.warning("No valid data found for any level in create_clean_csv_pc, creating empty output")
                result_df = pd.DataFrame(columns=["timestamp", "level", "name"])

            result_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)
            my_logger.info(f"Successfully created {filename}.csv with {len(result_df)} rows")

        except Exception as e:
            my_logger.error(f"Error in create_clean_csv_pc: {e}")
            # Create empty output file to prevent downstream errors
            empty_df = pd.DataFrame(columns=["timestamp", "level", "name"])
            empty_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)

    def create_clean_csv_cor_rate(self, df, filename):
        """
        Variante de l'export nettoyé, utilisant les noms de colonnes avec suffixe `_id`.
        pour exporter des données dans un csv depuis la bdd.

        Args:
            df (pd.DataFrame): Données à nettoyer.
            filename (str): Nom du fichier de sortie (sans extension).
        """
        my_logger = logging.getLogger("CBR-TABLE-CONSTRUCTION")

        # Defensive check: handle empty DataFrame
        if df is None or df.empty:
            my_logger.warning("Input DataFrame is empty for create_clean_csv_cor_rate, creating empty output file")
            empty_df = pd.DataFrame(columns=["timestamp", "level", "name"])
            empty_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)
            return

        try:
            # Defensive check: ensure required columns exist in input DataFrame
            required_cols = ["level", "entity_id"]
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                my_logger.warning(f"Missing required columns in input DataFrame for create_clean_csv_cor_rate: {missing_cols}")
                empty_df = pd.DataFrame(columns=["timestamp", "level", "name"])
                empty_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)
                return

            # Defensive preparation of reference DataFrames with proper column mapping
            subsidiary = self._prepare_reference_df(self.subsidiary, ["id", "long_name"], ["subsidiary_id", "name"], "subsidiary")
            cluster = self._prepare_reference_df(self.cluster, ["id", "name"], ["cluster_id", "name"], "cluster")
            division = self._prepare_reference_df(self.division, ["id", "long_name"], ["division_id", "name"], "division")
            asset = self._prepare_reference_df(self.asset, ["id", "name"], ["asset_id", "name"], "asset")

            # Defensive merging with proper column mapping
            data_frames = []

            # Handle subsidiary data
            if not df[df["level"] == "subsidiary"].empty and not subsidiary.empty:
                data_sub = safe_merge_with_type_conversion(
                    df[df["level"] == "subsidiary"], subsidiary,
                    left_on="entity_id", right_on="subsidiary_id", how="left")
                if not data_sub.empty:
                    data_frames.append(data_sub)

            # Handle cluster data
            if not df[df["level"] == "cluster"].empty and not cluster.empty:
                data_clust = safe_merge_with_type_conversion(
                    df[df["level"] == "cluster"], cluster,
                    left_on="entity_id", right_on="cluster_id", how="left")
                if not data_clust.empty:
                    data_frames.append(data_clust)

            # Handle division data
            if not df[df["level"] == "division"].empty and not division.empty:
                data_div = safe_merge_with_type_conversion(
                    df[df["level"] == "division"], division,
                    left_on="entity_id", right_on="division_id", how="left")
                if not data_div.empty:
                    data_frames.append(data_div)

            # Handle asset data
            if not df[df["level"] == "asset"].empty and not asset.empty:
                data_asset = safe_merge_with_type_conversion(
                    df[df["level"] == "asset"], asset,
                    left_on="entity_id", right_on="asset_id", how="left")
                if not data_asset.empty:
                    data_frames.append(data_asset)

            # Handle group data
            if not df[df["level"] == "group"].empty:
                data_group = df[df["level"] == "group"].copy()
                data_group = data_group.assign(name="Orange")
                data_frames.append(data_group)

            # Combine all data frames
            if data_frames:
                result_df = pd.concat(data_frames, ignore_index=True)
                # Clean up ID columns
                result_df.drop(columns=["subsidiary_id", "cluster_id", "division_id", "asset_id"],
                              inplace=True, errors="ignore")
            else:
                my_logger.warning("No valid data found for any level in create_clean_csv_cor_rate, creating empty output")
                result_df = pd.DataFrame(columns=["timestamp", "level", "name"])

            result_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)
            my_logger.info(f"Successfully created {filename}.csv with {len(result_df)} rows")

        except Exception as e:
            my_logger.error(f"Error in create_clean_csv_cor_rate: {e}")
            # Create empty output file to prevent downstream errors
            empty_df = pd.DataFrame(columns=["timestamp", "level", "name"])
            empty_df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"), index=False)

    def create_clean_csv_pch_h(self, df, filename):
        """
        Crée un fichier CSV avec les colonnes renommées en `<niveau>_name` pour chaque entité.

        Args:
            df (pd.DataFrame): Données d'entrée.
            filename (str): Nom du fichier de sortie (sans extension).
        """
        subsidiary = self.subsidiary[["id", "long_name"]]
        subsidiary.columns = ["subsidiary", "subsidiary_name"]
        cluster = self.cluster[["id", "name"]]
        cluster.columns = ["cluster", "cluster_name"]
        division = self.division[["id", "long_name"]]
        division.columns = ["division", "division_name"]
        asset = self.asset[["id", "name"]]
        asset.columns = ["asset", "asset_name"]
        df = df.merge(subsidiary, on="subsidiary", suffixes=('', '_drop'), how="left")
        df = df.merge(cluster, on="cluster", suffixes=('', '_drop'), how="left")
        df = df.merge(division, on="division", suffixes=('', '_drop'), how="left")
        df = df.merge(asset, on="asset", suffixes=('', '_drop'), how="left")
        df.to_csv(os.path.join(BASE_DIR, "out", f"{filename}.csv"))

    @staticmethod
    def create_vulnerable_ips_csv(df_public, df_private):
        """
        Génère un fichier CSV listant les IP vulnérables publiques et privées.

        Args:
            df_public (pd.DataFrame or None): Données publiques.
            df_private (pd.DataFrame or None): Données privées.
        """
        if df_public is None and df_private is None:
            df = pd.DataFrame(columns=["division_name", "cluster_name", "subsidiary_name", "IP_address",
                                       "ip_penalty", "info", "low", "medium", "high", "critical"])
        else:
            df = pd.concat([df_public, df_private])
            df = df.assign(timestamp=date.today())
        df.to_csv(os.path.join(BASE_DIR, "out", "vulnerable_ips.csv"))

    def attribute_rating_letters(self, df, plugin_level):
        """
        Attribue une lettre de note à chaque ligne selon le niveau de plugin et les seuils définis.

        Args:
            df (pd.DataFrame): Données d'entrée contenant les notes numériques.
            plugin_level (str): Niveau de plugin ('category', 'family').

        Returns:
            pd.DataFrame: Données enrichies d'une colonne `rating_letter`.
        """
        my_logger = logging.getLogger("CBR-GENERAL-CONSTRUCTION")

        # Defensive check: handle empty DataFrame
        if df is None or df.empty:
            my_logger.warning("Input DataFrame is empty, returning empty DataFrame with rating_letter column")
            return pd.DataFrame(columns=list(df.columns) + ['rating_letter'] if df is not None else ['rating_letter'])

        # Defensive check: ensure rating_letters is available
        if not hasattr(self, 'rating_letters') or self.rating_letters is None or self.rating_letters.empty:
            my_logger.warning("rating_letters is empty, adding default rating_letter column")
            df["rating_letter"] = "N/A"
            return df

        # Defensive check: ensure required column exists
        rating_col = plugin_level + "_rating"
        if rating_col not in df.columns:
            my_logger.warning(f"Column {rating_col} not found in DataFrame, adding default rating_letter column")
            df["rating_letter"] = "N/A"
            return df

        try:
            # Defensive check: ensure rating_letters has required columns
            if 'value' not in self.rating_letters.columns or 'id' not in self.rating_letters.columns:
                my_logger.warning("rating_letters missing required columns, adding default rating_letter column")
                df["rating_letter"] = "N/A"
                return df

            bins = self.rating_letters.value.to_list() + [101]
            labels = self.rating_letters.id.to_list()

            # Defensive check: handle NaN values in rating column
            if df[rating_col].isna().all():
                my_logger.warning(f"All values in {rating_col} are NaN, setting rating_letter to N/A")
                df["rating_letter"] = "N/A"
                return df

            df["rating_letter"] = pd.cut(df[rating_col],
                                         bins=bins,
                                         labels=labels,
                                         right=False)
            return df

        except Exception as e:
            my_logger.error(f"Error in attribute_rating_letters: {e}")
            df["rating_letter"] = "N/A"
            return df

    @staticmethod
    def log_wa(rating, size_parc):
        """
        Calcule une moyenne pondérée logarithmique.

        Args:
            rating (pd.Series): Série des notes.
            size_parc (pd.Series): Série des tailles de parc associées.

        Returns:
            float: Moyenne pondérée logarithmique.
        """
        valid_mask = rating != -1
        if not valid_mask.any():
            return -1

        rating = rating[valid_mask]
        size_parc = size_parc[valid_mask]

        weights = (size_parc + 1).apply(math.log)
        return np.average(rating, weights=weights)

    # # TODO : P-E faire seulement au niveau sub et prolonger sur les autres niveaux
    def create_all_level(self, plugin_level, df_public, df_private):
        """
        Calcule les moyennes pondérées de notation pour chaque niveau (asset, division, etc.)

        Args:
            plugin_level (str): Niveau de plugin concerné ('category', 'family', etc.).
            df_public (pd.DataFrame): Données publiques.
            df_private (pd.DataFrame): Données privées.

        Returns:
            pd.DataFrame: Données concaténées avec les notations agrégées pour tous les niveaux.
        """
        my_logger = logging.getLogger("CBR-GENERAL-CONSTRUCTION")

        # Defensive check: handle None or empty DataFrames
        if df_public is None:
            df_public = pd.DataFrame()
        if df_private is None:
            df_private = pd.DataFrame()

        # Defensive filtering with column existence check
        if not df_public.empty and "parc_size" in df_public.columns:
            df_public = df_public[df_public["parc_size"] >= 1]
        if not df_private.empty and "parc_size" in df_private.columns:
            df_private = df_private[df_private["parc_size"] >= 1]

        # Both DataFrames are empty after filtering
        if df_public.empty and df_private.empty:
            my_logger.warning("Both public and private DataFrames are empty after filtering")
            return pd.DataFrame()

        if plugin_level == "family":
            plugin_list = [plugin_level]
        elif plugin_level == "category":
            plugin_list = [plugin_level, "family"]
        else:
            plugin_list = []

        list_levels = self.list_level + ["group", "asset"]
        common_cols_list = ["timestamp", "level"] + plugin_list + ["group"]
        df_all = pd.DataFrame()

        for level in list_levels:
            try:
                if level == "asset":
                    list_gb = common_cols_list + ["asset"]
                else:
                    list_gb = common_cols_list + self.list_level

                all_columns = pd.Index([])

                # Defensive filtering with column existence check
                df_public_level = df_public[df_public["level"] == level] if not df_public.empty and "level" in df_public.columns else pd.DataFrame()
                df_private_level = df_private[df_private["level"] == level] if not df_private.empty and "level" in df_private.columns else pd.DataFrame()

                df_list = [df_public_level, df_private_level]
                df_list = [df for df in df_list if df is not None]
                for df in df_list:
                    all_columns = all_columns.union(df.columns)
                df_non_empty = [df for df in df_list if not df.isna().all().all()]
                if df_non_empty:
                    temp = pd.concat(df_non_empty, ignore_index=True)
                    temp = temp.reindex(columns=all_columns)
                else:
                    temp = pd.DataFrame()
                    temp = temp.reindex(columns=all_columns)

                # Defensive groupby with column existence check
                if not temp.empty:
                    # Check if required columns exist for groupby
                    required_cols = [plugin_level + "_rating", "parc_size"]
                    missing_cols = [col for col in required_cols if col not in temp.columns]
                    if missing_cols:
                        my_logger.warning(f"Missing columns for level {level}: {missing_cols}")
                        continue

                    # Check if groupby columns exist
                    groupby_cols = list_gb[: list_gb.index(level) + 1]
                    missing_gb_cols = [col for col in groupby_cols if col not in temp.columns]
                    if missing_gb_cols:
                        my_logger.warning(f"Missing groupby columns for level {level}: {missing_gb_cols}")
                        continue

                    gb = temp.groupby(groupby_cols).apply(
                        lambda x: pd.Series(
                            {plugin_level + "_rating": self.log_wa(x[plugin_level + "_rating"], x["parc_size"])}))
                    if not gb.empty:
                        gb = gb.reset_index()
                        df_all = pd.concat([df_all, gb], ignore_index=True)

            except Exception as e:
                my_logger.error(f"Error processing level {level}: {e}")
                continue

        # Defensive assignment of privacy
        if not df_all.empty:
            df_all = df_all.assign(privacy="all")

        df_list = [df_public, df_private, df_all]
        df_list = [df for df in df_list if df is not None]
        all_columns = pd.Index([])
        for df in df_list:
            all_columns = all_columns.union(df.columns)
        df_non_empty = [df for df in df_list if not df.isna().all().all()]
        if df_non_empty:
            temp = pd.concat(df_non_empty, ignore_index=True)
            df_full = temp.reindex(columns=all_columns)
        else:
            temp = pd.DataFrame()
            df_full = temp.reindex(columns=all_columns)

        # Defensive column dropping
        for col in ["parc_size", "issued_ips", "parc_size_sum"]:
            if col in df_full.columns:
                df_full.drop(col, axis=1, inplace=True)

        return df_full

    @staticmethod
    def create_data_findings(entities_level, df_public, df_private):
        """
        Prépare les données d'observations (findings) pour un niveau d'entité donné.

        Args:
            entities_level (str): Niveau ciblé ('entities' ou 'asset').
            df_public (pd.DataFrame): Données publiques.
            df_private (pd.DataFrame): Données privées.

        Returns:
            pd.DataFrame: Données consolidées contenant les findings.
        """
        my_logger = logging.getLogger("CBR-GENERAL-CONSTRUCTION")

        # Defensive check: handle None inputs
        if df_public is None:
            df_public = pd.DataFrame()
        if df_private is None:
            df_private = pd.DataFrame()

        # Both DataFrames are empty
        if df_public.empty and df_private.empty:
            my_logger.warning(f"Both public and private DataFrames are empty for {entities_level} findings")
            # Return empty DataFrame with proper structure
            if entities_level == "entities":
                entities_list = ["subsidiary", "cluster", "division"]
            else:
                entities_list = ["asset"]

            columns = ["timestamp", "group", "severity", "ip_id", "orange_cyber_rating_category_id",
                      "orange_cyber_rating_family_id", "plugin_id", "privacy"] + entities_list
            return pd.DataFrame(columns=columns)

        try:
            # Use defensive concatenation pattern
            df_list = [df_public, df_private]
            df_list = [df for df in df_list if df is not None and not df.empty]

            if df_list:
                df = pd.concat(df_list, ignore_index=True)
            else:
                my_logger.warning(f"No valid DataFrames to concatenate for {entities_level} findings")
                if entities_level == "entities":
                    entities_list = ["subsidiary", "cluster", "division"]
                else:
                    entities_list = ["asset"]

                columns = ["timestamp", "group", "severity", "ip_id", "orange_cyber_rating_category_id",
                          "orange_cyber_rating_family_id", "plugin_id", "privacy"] + entities_list
                return pd.DataFrame(columns=columns)

            df = df.assign(group="Orange")

            # Defensive column renaming
            if "scan_date" in df.columns:
                df.rename(columns={"scan_date": "timestamp"}, inplace=True)
            elif "timestamp" not in df.columns:
                # Add timestamp column if missing
                df["timestamp"] = pd.NaT

            if entities_level == "entities":
                entities_list = ["subsidiary", "cluster", "division"]
            else:
                entities_list = ["asset"]

            # Defensive column selection - only select columns that exist
            required_cols = ["timestamp", "group", "severity", "ip_id", "orange_cyber_rating_category_id",
                           "orange_cyber_rating_family_id", "plugin_id", "privacy"] + entities_list
            available_cols = [col for col in required_cols if col in df.columns]

            if available_cols:
                df = df[available_cols]
                # Add missing columns with default values
                for col in required_cols:
                    if col not in df.columns:
                        if col in ["timestamp"]:
                            df[col] = pd.NaT
                        elif col in ["group"]:
                            df[col] = "Orange"
                        else:
                            df[col] = None
            else:
                my_logger.warning(f"No required columns available for {entities_level} findings")
                df = pd.DataFrame(columns=required_cols)

            return df

        except Exception as e:
            my_logger.error(f"Error creating findings for {entities_level}: {e}")
            # Return empty DataFrame with proper structure
            if entities_level == "entities":
                entities_list = ["subsidiary", "cluster", "division"]
            else:
                entities_list = ["asset"]

            columns = ["timestamp", "group", "severity", "ip_id", "orange_cyber_rating_category_id",
                      "orange_cyber_rating_family_id", "plugin_id", "privacy"] + entities_list
            return pd.DataFrame(columns=columns)

    @staticmethod
    def create_pc_db(df_public, df_private):
        """
        Fusionne les données publiques et privées en respectant les colonnes présentes.

        Args:
            df_public (pd.DataFrame): Données publiques.
            df_private (pd.DataFrame): Données privées.

        Returns:
            pd.DataFrame: Données fusionnées avec structure homogénéisée.
        """
        df_list = [df_public, df_private]
        df_list = [df for df in df_list if df is not None]
        # Identifier l'ensemble des colonnes présentes dans les DataFrames
        all_columns = pd.Index([])
        for df in df_list:
            all_columns = all_columns.union(df.columns)
        df_non_empty = [df for df in df_list if not df.isna().all().all()]
        if df_non_empty:
            df = pd.concat(df_non_empty, ignore_index=True)
            df = df.reindex(columns=all_columns)
        else:
            df = pd.DataFrame()
            df = df.reindex(columns=all_columns)
        return df

    @staticmethod
    def merge_ip_fqdn(df_ip: pd.DataFrame, df_fqdn: pd.DataFrame):
        """
        Merges IP and FQDN DataFrames with defensive programming patterns.

        Args:
            df_ip (pd.DataFrame): IP data DataFrame
            df_fqdn (pd.DataFrame): FQDN data DataFrame

        Returns:
            pd.DataFrame: Merged DataFrame with resource_type column
        """
        my_logger = logging.getLogger("CBR-GENERAL-CONSTRUCTION")

        # Defensive check: handle None inputs
        if df_ip is None:
            df_ip = pd.DataFrame()
        if df_fqdn is None:
            df_fqdn = pd.DataFrame()

        # Both DataFrames are empty
        if df_ip.empty and df_fqdn.empty:
            my_logger.warning("Both IP and FQDN DataFrames are empty")
            return pd.DataFrame(columns=['resource_type'])

        try:
            # Defensive assignment of resource_type
            if not df_ip.empty:
                df_ip = df_ip.copy()  # Avoid modifying original DataFrame
                df_ip.loc[:, "resource_type"] = 0
            if not df_fqdn.empty:
                df_fqdn = df_fqdn.copy()  # Avoid modifying original DataFrame
                df_fqdn.loc[:, "resource_type"] = 1

            df_list = [df_ip, df_fqdn]
            df_list = [df for df in df_list if df is not None]
            # Identifier l'ensemble des colonnes présentes dans les DataFrames
            all_columns = pd.Index([])
            for df in df_list:
                all_columns = all_columns.union(df.columns)
            df_non_empty = [df for df in df_list if not df.isna().all().all()]
            if df_non_empty:
                df = pd.concat(df_non_empty, ignore_index=True)
                df = df.reindex(columns=all_columns)
            else:
                df = pd.DataFrame()
                df = df.reindex(columns=all_columns)
            return df

        except Exception as e:
            my_logger.error(f"Error in merge_ip_fqdn: {e}")
            return pd.DataFrame(columns=['resource_type'])


if __name__ == "__main__":
    pd.set_option("display.max_columns", 500)
    pd.set_option("display.max_rows", 500)
