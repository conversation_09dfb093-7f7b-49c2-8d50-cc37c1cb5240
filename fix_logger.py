#!/usr/bin/env python3
"""
Script to fix logger references in correction.py
"""

import re

def fix_logger_references():
    file_path = "IA_Cyberrating/scripts/src/correction.py"
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace all standalone my_logger with self.my_logger (but not self.my_logger)
    # This regex looks for my_logger that is not preceded by self.
    content = re.sub(r'(?<!self\.)my_logger', 'self.my_logger', content)
    
    # Remove local logger declarations
    content = re.sub(r'\s*my_logger = logging\.getLogger\("CBR-CORRECTION"\)\s*\n', '', content)
    
    # Write the file back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Fixed logger references in correction.py")

if __name__ == "__main__":
    fix_logger_references()
