#!/usr/bin/env python3
"""
Comprehensive test script to verify all defensive programming patterns
are working correctly in the cyber rating pipeline.

This script tests:
1. Missing Column Errors handling
2. Empty DataFrame handling  
3. Data Type Validation
4. Graceful Degradation patterns
"""

import pandas as pd
import numpy as np
import logging
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.table_construction import GeneralConstruction, TableConstruction
from src.correction import Correction
from src.import_data import DataImport
from main import (
    data_category_concatenation, data_family_concatenation, data_total_concatenation,
    rating_letters_attribution, category_correction, family_correction, ip_correction,
    useless_col_schema, useless_col_schema2
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DEFENSIVE-TEST")

def create_mock_data_import():
    """Create a mock DataImport object with empty/minimal data"""
    class MockDataImport:
        def __init__(self):
            # Create empty DataFrames with minimal required structure
            self.ref_rt = pd.DataFrame(columns=['id', 'is_public'])
            self.ref_rt_asset = pd.DataFrame(columns=['ip_id', 'asset', 'id', 'is_public'])
            self.data_ip_grade = pd.DataFrame(columns=['ip_id'])
            
            # Organizational data - empty but with proper structure
            self.asset = pd.DataFrame(columns=['id', 'name'])
            self.subsidiary = pd.DataFrame(columns=['id', 'long_name'])
            self.cluster = pd.DataFrame(columns=['id', 'name'])
            self.division = pd.DataFrame(columns=['id', 'long_name'])
            self.category = pd.DataFrame(columns=['id', 'name', 'activate'])
            self.family = pd.DataFrame(columns=['id', 'name', 'activate'])
            
            # Mapping and other required attributes
            self.mapping = pd.DataFrame(columns=['field_value', 'orange_cyber_rating_category_id', 'orange_cyber_rating_family_id'])
            self.rating_letters = pd.DataFrame(columns=['plugin_level', 'rating_min', 'rating_max', 'rating_letter'])
            
            # Scan data - empty
            self.data_ip = pd.DataFrame()
            self.data_ip_asset = pd.DataFrame()
            self.pc_history = pd.DataFrame()
            
    return MockDataImport()

def test_empty_dataframe_handling():
    """Test that functions handle empty DataFrames gracefully"""
    logger.info("Testing empty DataFrame handling...")
    
    # Test with completely empty DataFrames
    empty_df = pd.DataFrame()
    
    # Test useless_col_schema functions
    try:
        cat, fam, tot = useless_col_schema(empty_df, empty_df, empty_df)
        logger.info("✓ useless_col_schema handles empty DataFrames")
    except Exception as e:
        logger.error(f"✗ useless_col_schema failed with empty DataFrames: {e}")
    
    try:
        cat_cor, fam_cor, ip_cor, ip_cor_fqdn = useless_col_schema2(empty_df, empty_df, empty_df, empty_df)
        logger.info("✓ useless_col_schema2 handles empty DataFrames")
    except Exception as e:
        logger.error(f"✗ useless_col_schema2 failed with empty DataFrames: {e}")

def test_missing_column_handling():
    """Test handling of missing columns in DataFrames"""
    logger.info("Testing missing column handling...")
    
    # Create DataFrames with missing expected columns
    df_missing_cluster = pd.DataFrame({
        'level': ['subsidiary', 'division'],
        'subsidiary': [1, 2],
        'division': [10, 20],
        # Missing 'cluster' column
        'total_rating': [5.0, 6.0]
    })
    
    # Test TableConstruction with missing columns
    try:
        mock_import = create_mock_data_import()
        tc = TableConstruction("public", mock_import)
        logger.info("✓ TableConstruction handles missing columns in initialization")
    except Exception as e:
        logger.error(f"✗ TableConstruction failed with missing columns: {e}")

def test_none_value_handling():
    """Test handling of None values and NoneType objects"""
    logger.info("Testing None value handling...")
    
    # Test with None inputs
    try:
        cat, fam, tot = useless_col_schema(None, None, None)
        logger.info("✓ useless_col_schema handles None inputs")
    except Exception as e:
        logger.error(f"✗ useless_col_schema failed with None inputs: {e}")
    
    try:
        cat_cor, fam_cor, ip_cor, ip_cor_fqdn = useless_col_schema2(None, None, None, None)
        logger.info("✓ useless_col_schema2 handles None inputs")
    except Exception as e:
        logger.error(f"✗ useless_col_schema2 failed with None inputs: {e}")

def test_data_type_validation():
    """Test data type validation and conversion"""
    logger.info("Testing data type validation...")
    
    # Create DataFrames with mixed data types that could cause merge issues
    df1 = pd.DataFrame({
        'id': [1, 2, 3],  # int
        'name': ['A', 'B', 'C']
    })
    
    df2 = pd.DataFrame({
        'id': ['1', '2', '4'],  # string
        'value': [10, 20, 30]
    })
    
    # Test safe merge functionality
    try:
        from src.utils.utils import safe_merge_with_type_conversion
        result = safe_merge_with_type_conversion(df1, df2, on='id', how='inner')
        logger.info("✓ safe_merge_with_type_conversion handles type mismatches")
    except Exception as e:
        logger.error(f"✗ safe_merge_with_type_conversion failed: {e}")

def test_graceful_degradation():
    """Test that the system degrades gracefully with missing data"""
    logger.info("Testing graceful degradation...")
    
    # Test GeneralConstruction with empty data
    try:
        mock_import = create_mock_data_import()
        gc = GeneralConstruction(mock_import)
        
        # Test create_all_level with empty data
        result = gc.create_all_level("category", pd.DataFrame(), pd.DataFrame())
        logger.info("✓ GeneralConstruction.create_all_level handles empty data")
        
        # Test merge_ip_fqdn with empty data
        result = gc.merge_ip_fqdn(pd.DataFrame(), pd.DataFrame())
        logger.info("✓ GeneralConstruction.merge_ip_fqdn handles empty data")
        
    except Exception as e:
        logger.error(f"✗ GeneralConstruction graceful degradation failed: {e}")

def test_cluster_column_access():
    """Test defensive access to cluster-related columns"""
    logger.info("Testing cluster column access...")
    
    # Create DataFrame without cluster columns
    df_no_cluster = pd.DataFrame({
        'level': ['subsidiary', 'division'],
        'subsidiary': [1, 2],
        'division': [10, 20],
        'total_rating': [5.0, 6.0]
    })
    
    # Test that functions don't crash when cluster columns are missing
    try:
        # This should not crash even without cluster columns
        mock_import = create_mock_data_import()
        gc = GeneralConstruction(mock_import)
        result = gc.create_data_today_csv(df_no_cluster)
        logger.info("✓ create_data_today_csv handles missing cluster columns")
    except Exception as e:
        logger.error(f"✗ create_data_today_csv failed with missing cluster columns: {e}")

def run_all_tests():
    """Run all defensive programming tests"""
    logger.info("Starting comprehensive defensive programming tests...")
    
    test_empty_dataframe_handling()
    test_missing_column_handling()
    test_none_value_handling()
    test_data_type_validation()
    test_graceful_degradation()
    test_cluster_column_access()
    
    logger.info("Comprehensive defensive programming tests completed!")

if __name__ == "__main__":
    run_all_tests()
