import logging
import pandas as pd
import numpy as np
from src import DataImport, TableConstruction, GeneralConstruction, Correction, DataExport, ADrating
from src import send_csv_mail, config_logger, log_decorator, get_version_ref
from src.utils.utils import check_sources

# Initializing the logger
my_logger = logging.getLogger("CBR-MAIN")


@log_decorator("Step 1/9: Importing all databases ...")
def import_data() -> DataImport:
    """
    Runs the DataImport class

    Returns
    -------
    import_class : class
        The DataImport class
    """

    import_class = DataImport(fqdn=False)
    import_fqdn_class = DataImport(fqdn=True)
    return import_class, import_fqdn_class


@log_decorator("2-1: Initializing data ...")
def data_initialisation(tables_construction_class: TableConstruction,
                        tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the initializing_data function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.initializing_data()
    tables_construction_fqdn.initializing_data()

    # Defensive check: ensure data_ip_grade exists before renaming columns
    if hasattr(tables_construction_fqdn, 'data_ip_grade') and tables_construction_fqdn.data_ip_grade is not None:
        if not tables_construction_fqdn.data_ip_grade.empty and 'ip_id' in tables_construction_fqdn.data_ip_grade.columns:
            tables_construction_fqdn.data_ip_grade.rename(columns={"ip_id": "fqdn_id"}, inplace=True)
        else:
            my_logger.warning("data_ip_grade is empty or missing ip_id column, skipping rename")
    else:
        my_logger.warning("data_ip_grade attribute missing in tables_construction_fqdn, skipping rename")
    # return tables_construction_class.data_ip_grade, tables_construction_fqdn.data_ip_grade


@log_decorator("2-2: Creating data category ...")
def data_category_creation(tables_construction_class: TableConstruction,
                           tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the create_data_category function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.create_data_category()
    tables_construction_fqdn.create_data_category()


@log_decorator("2-3: Creating data family ...")
def data_family_creation(tables_construction_class: TableConstruction,
                         tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the create_data_family function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.create_data_family()
    tables_construction_fqdn.create_data_family()


@log_decorator("2-4: Creating data total ...")
def data_total_creation(tables_construction_class: TableConstruction,
                        tables_construction_fqdn: TableConstruction) -> None:
    """
    Runs the create_data_total function from TableConstruction class

    Parameters
    ----------
    tables_construction_class : class
        The TableConstruction class
    """

    tables_construction_class.create_data_total()
    tables_construction_fqdn.create_data_total()


def privacy_tables_construction(privacy: str, import_class: DataImport,
                                import_fqdn_class: DataImport) -> TableConstruction:
    """
    Runs all the functions to create rating tables according to the privacy

    Parameters
    ----------
    privacy : str
        The ip privacy, public or private
    import_class : class
        The DataImport class

    Returns
    -------
    tables_construction_class : class
        The TableConstruction class according to the privacy
    """

    my_logger.info("Step 2/9: Rating on privacy: " + privacy)
    tables_construction_class = TableConstruction(privacy, import_class)
    tables_construction_fqdn = TableConstruction(privacy, import_fqdn_class)

    data_initialisation(tables_construction_class, tables_construction_fqdn)
    data_category_creation(tables_construction_class, tables_construction_fqdn)
    data_family_creation(tables_construction_class, tables_construction_fqdn)
    data_total_creation(tables_construction_class, tables_construction_fqdn)
    return tables_construction_class, tables_construction_fqdn


@log_decorator("3-1: Concatenating data category ...")
def data_category_concatenation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private category rating dataframe and creates a global category rating

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction class associate to fqdn
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    df_cat : DataFrame
        The category rating dataframe with global rating
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure data_category exists and is not None
    public_data_category = getattr(public, 'data_category', pd.DataFrame()) if public else pd.DataFrame()
    private_data_category = getattr(private, 'data_category', pd.DataFrame()) if private else pd.DataFrame()
    public_fqdn_data_category = getattr(public_fqdn, 'data_category', pd.DataFrame()) if public_fqdn else pd.DataFrame()
    private_fqdn_data_category = getattr(private_fqdn, 'data_category', pd.DataFrame()) if private_fqdn else pd.DataFrame()

    try:
        df = construct.create_all_level("category", public_data_category, private_data_category)
        # Defensive assignment with empty check
        if not df.empty:
            df = df.assign(resource_type=0)
        else:
            df = pd.DataFrame(columns=['resource_type'])
            df.loc[0, 'resource_type'] = 0

        df_fqdn = construct_fqdn.create_all_level("category", public_fqdn_data_category, private_fqdn_data_category)
        # Defensive assignment with empty check
        if not df_fqdn.empty:
            df_fqdn = df_fqdn.assign(resource_type=1)
        else:
            df_fqdn = pd.DataFrame(columns=['resource_type'])
            df_fqdn.loc[0, 'resource_type'] = 1

        df_cat = construct.merge_ip_fqdn(df, df_fqdn)

        # Defensive check: ensure result is not None
        if df_cat is None:
            my_logger.warning("merge_ip_fqdn returned None, creating empty DataFrame")
            df_cat = pd.DataFrame()

        return df_cat

    except Exception as e:
        my_logger.error(f"Error in data_category_concatenation: {e}")
        return pd.DataFrame()


@log_decorator("3-2: Concatenating data family ...")
def data_family_concatenation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private family rating dataframe and creates a global family rating

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction class associate to fqdn
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    df_fam : DataFrame
        The family rating dataframe with global rating
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure data_family exists and is not None
    public_data_family = getattr(public, 'data_family', pd.DataFrame()) if public else pd.DataFrame()
    private_data_family = getattr(private, 'data_family', pd.DataFrame()) if private else pd.DataFrame()
    public_fqdn_data_family = getattr(public_fqdn, 'data_family', pd.DataFrame()) if public_fqdn else pd.DataFrame()
    private_fqdn_data_family = getattr(private_fqdn, 'data_family', pd.DataFrame()) if private_fqdn else pd.DataFrame()

    try:
        df = construct.create_all_level("family", public_data_family, private_data_family)
        # Defensive assignment with empty check
        if not df.empty:
            df = df.assign(resource_type=0)
        else:
            df = pd.DataFrame(columns=['resource_type'])
            df.loc[0, 'resource_type'] = 0

        df_fqdn = construct_fqdn.create_all_level("family", public_fqdn_data_family, private_fqdn_data_family)
        # Defensive assignment with empty check
        if not df_fqdn.empty:
            df_fqdn = df_fqdn.assign(resource_type=1)
        else:
            df_fqdn = pd.DataFrame(columns=['resource_type'])
            df_fqdn.loc[0, 'resource_type'] = 1

        df_fam = construct.merge_ip_fqdn(df, df_fqdn)

        # Defensive check: ensure result is not None
        if df_fam is None:
            my_logger.warning("merge_ip_fqdn returned None, creating empty DataFrame")
            df_fam = pd.DataFrame()

        return df_fam

    except Exception as e:
        my_logger.error(f"Error in data_family_concatenation: {e}")
        return pd.DataFrame()


@log_decorator("3-3: Concatenating data total ...")
def data_total_concatenation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private total rating dataframe and creates a global total rating

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction class associate to fqdn
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    df : DataFrame
        The total rating dataframe with global rating
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure data_total exists and is not None
    public_data_total = getattr(public, 'data_total', pd.DataFrame()) if public else pd.DataFrame()
    private_data_total = getattr(private, 'data_total', pd.DataFrame()) if private else pd.DataFrame()
    public_fqdn_data_total = getattr(public_fqdn, 'data_total', pd.DataFrame()) if public_fqdn else pd.DataFrame()
    private_fqdn_data_total = getattr(private_fqdn, 'data_total', pd.DataFrame()) if private_fqdn else pd.DataFrame()

    try:
        df = construct.create_all_level("total", public_data_total, private_data_total)
        # Defensive assignment with empty check
        if not df.empty:
            df = df.assign(resource_type=0)
        else:
            df = pd.DataFrame(columns=['resource_type'])
            df.loc[0, 'resource_type'] = 0

        df_fqdn = construct_fqdn.create_all_level("total", public_fqdn_data_total, private_fqdn_data_total)
        # Defensive assignment with empty check
        if not df_fqdn.empty:
            df_fqdn = df_fqdn.assign(resource_type=1)
        else:
            df_fqdn = pd.DataFrame(columns=['resource_type'])
            df_fqdn.loc[0, 'resource_type'] = 1

        df_total = construct.merge_ip_fqdn(df, df_fqdn)

        # Defensive check: ensure result is not None
        if df_total is None:
            my_logger.warning("merge_ip_fqdn returned None, creating empty DataFrame")
            df_total = pd.DataFrame()

        return df_total

    except Exception as e:
        my_logger.error(f"Error in data_total_concatenation: {e}")
        return pd.DataFrame()


@log_decorator("3-4: Attributing rating letters ...")
def rating_letters_attribution(
        construct: GeneralConstruction,
        data_category: pd.DataFrame,
        data_family: pd.DataFrame,
        data_total: pd.DataFrame):
    """
    Attributes rating letters to dataframes

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    data_category : DataFrame
        The category rating dataframe
    data_family : DataFrame
        The family rating dataframe
    data_total : DataFrame
        The total rating dataframe

    Returns
    -------
    data_category : DataFrame
        The category rating dataframe with rating letters
    data_family : DataFrame
        The family rating dataframe with rating letters
    data_total : DataFrame
        The total rating dataframe with rating letters
    """

    my_logger = logging.getLogger("CBR-MAIN")

    # Defensive check: ensure DataFrames are not None
    if data_category is None:
        data_category = pd.DataFrame()
    if data_family is None:
        data_family = pd.DataFrame()
    if data_total is None:
        data_total = pd.DataFrame()

    try:
        data_category = construct.attribute_rating_letters(data_category, "category")
        data_family = construct.attribute_rating_letters(data_family, "family")
        data_total = construct.attribute_rating_letters(data_total, "total")

        return data_category, data_family, data_total

    except Exception as e:
        my_logger.error(f"Error in rating_letters_attribution: {e}")
        # Return empty DataFrames with rating_letter column
        return (pd.DataFrame(columns=['rating_letter']),
                pd.DataFrame(columns=['rating_letter']),
                pd.DataFrame(columns=['rating_letter']))


@log_decorator("3-5: Creating data findings ...")
def findings_creation(construct: GeneralConstruction,
                      construct_fqdn: GeneralConstruction,
                      public: TableConstruction,
                      private: TableConstruction,
                      public_fqdn: TableConstruction,
                      private_fqdn: TableConstruction):
    """
    Concatenates public and private findings and findings assets dataframe

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction fqdn class
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private fqdn

    Returns
    -------
    findings : DataFrame
        The dataframe with all scans for entities since 45 days
    findings_asset : DataFrame
        The dataframe with all scans for assets since 45 days
    findings_fqdn : DataFrame
        The dataframe with all scans for fqdn entities since 45 days
    findings_fqdn_asset : DataFrame
        The dataframe with all scans for fqdn assets since 45 days
    """

    my_logger = logging.getLogger("CBR-MAIN")

    try:
        # Defensive data access
        public_data = getattr(public, 'data', pd.DataFrame()) if public else pd.DataFrame()
        private_data = getattr(private, 'data', pd.DataFrame()) if private else pd.DataFrame()
        public_data_asset = getattr(public, 'data_asset', pd.DataFrame()) if public else pd.DataFrame()
        private_data_asset = getattr(private, 'data_asset', pd.DataFrame()) if private else pd.DataFrame()

        public_fqdn_data = getattr(public_fqdn, 'data', pd.DataFrame()) if public_fqdn else pd.DataFrame()
        private_fqdn_data = getattr(private_fqdn, 'data', pd.DataFrame()) if private_fqdn else pd.DataFrame()
        public_fqdn_data_asset = getattr(public_fqdn, 'data_asset', pd.DataFrame()) if public_fqdn else pd.DataFrame()
        private_fqdn_data_asset = getattr(private_fqdn, 'data_asset', pd.DataFrame()) if private_fqdn else pd.DataFrame()

        findings = construct.create_data_findings("entities", public_data, private_data)
        findings_asset = construct.create_data_findings("asset", public_data_asset, private_data_asset)

        findings_fqdn = construct_fqdn.create_data_findings("entities", public_fqdn_data, private_fqdn_data)
        # Defensive column renaming
        if "ip_id" in findings_fqdn.columns:
            findings_fqdn.rename(columns={"ip_id": "fqdn_id"}, inplace=True)

        findings_fqdn_asset = construct_fqdn.create_data_findings("asset", public_fqdn_data_asset, private_fqdn_data_asset)
        # Defensive column renaming
        if "ip_id" in findings_fqdn_asset.columns:
            findings_fqdn_asset.rename(columns={"ip_id": "fqdn_id"}, inplace=True)

        # Ensure all results are DataFrames (not None)
        if findings is None:
            findings = pd.DataFrame()
        if findings_asset is None:
            findings_asset = pd.DataFrame()
        if findings_fqdn is None:
            findings_fqdn = pd.DataFrame()
        if findings_fqdn_asset is None:
            findings_fqdn_asset = pd.DataFrame()

        return findings, findings_asset, findings_fqdn, findings_fqdn_asset

    except Exception as e:
        my_logger.error(f"Error in findings_creation: {e}")
        # Return tuple of 4 empty DataFrames to prevent unpacking errors
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()


@log_decorator("3-6: Creating data patching cadence ...")
def patching_cadence_creation(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        public_fqdn: TableConstruction,
        private_fqdn: TableConstruction) -> pd.DataFrame:
    """
    Concatenates public and private patching cadence dataframe

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    construct_fqdn : class
        The GeneralConstruction fqdn class
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    public_fqdn : class
        The TableConstruction class with public fqdn
    private_fqdn : class
        The TableConstruction class with private f
    Returns
    -------
    severity_pc_mean : DataFrame
        The dataframe about mean correction delay by severity
    pc_history : DataFrame
        The dataframe with all scans history and information about correction for ips
    pc_history_fqdn : DataFrame
        The dataframe with all scans history and information about correction for fqdn
    pc_rate : DataFrame
        The dataframe with about the proportion of correction by severity
    """
    my_logger = logging.getLogger("CBR-MAIN")

    try:
        # Defensive data access
        public_pc_history = getattr(public, 'pc_history', pd.DataFrame()) if public else pd.DataFrame()
        private_pc_history = getattr(private, 'pc_history', pd.DataFrame()) if private else pd.DataFrame()
        public_fqdn_pc_history = getattr(public_fqdn, 'pc_history', pd.DataFrame()) if public_fqdn else pd.DataFrame()
        private_fqdn_pc_history = getattr(private_fqdn, 'pc_history', pd.DataFrame()) if private_fqdn else pd.DataFrame()

        pc_history = construct.create_pc_db(public_pc_history, private_pc_history)
        if pc_history is None:
            pc_history = pd.DataFrame()
        pc_history.drop(["level_6", "level_8"], axis=1, inplace=True, errors="ignore")

        # Defensive method calls
        if public and hasattr(public, 'create_severity_pc_mean_table'):
            public.create_severity_pc_mean_table()
        if private and hasattr(private, 'create_severity_pc_mean_table'):
            private.create_severity_pc_mean_table()

        public_pc_db_sm = getattr(public, 'pc_db_sm', pd.DataFrame()) if public else pd.DataFrame()
        private_pc_db_sm = getattr(private, 'pc_db_sm', pd.DataFrame()) if private else pd.DataFrame()

        severity_pc_mean = construct.create_pc_db(public_pc_db_sm, private_pc_db_sm)
        if severity_pc_mean is None:
            severity_pc_mean = pd.DataFrame()
        if not severity_pc_mean.empty:
            severity_pc_mean = severity_pc_mean.assign(resource_type=0)

        # Defensive method calls for correction rate
        if public and hasattr(public, 'create_correction_rate'):
            public.create_correction_rate()
        if private and hasattr(private, 'create_correction_rate'):
            private.create_correction_rate()

        public_pc_rate = getattr(public, 'pc_rate', pd.DataFrame()) if public else pd.DataFrame()
        private_pc_rate = getattr(private, 'pc_rate', pd.DataFrame()) if private else pd.DataFrame()

        pc_rate = construct.create_pc_db(public_pc_rate, private_pc_rate)
        if pc_rate is None:
            pc_rate = pd.DataFrame()

        pc_history_fqdn = construct_fqdn.create_pc_db(public_fqdn_pc_history, private_fqdn_pc_history)
        if pc_history_fqdn is None:
            pc_history_fqdn = pd.DataFrame()
        pc_history_fqdn.drop(["level_6", "level_8"], axis=1, inplace=True, errors="ignore")
        # Defensive column renaming
        if "ip_id" in pc_history_fqdn.columns:
            pc_history_fqdn.rename(columns={"ip_id": "fqdn_id"}, inplace=True)

        # Defensive method calls for FQDN
        if public_fqdn and hasattr(public_fqdn, 'create_severity_pc_mean_table'):
            public_fqdn.create_severity_pc_mean_table()
        if private_fqdn and hasattr(private_fqdn, 'create_severity_pc_mean_table'):
            private_fqdn.create_severity_pc_mean_table()

        public_fqdn_pc_db_sm = getattr(public_fqdn, 'pc_db_sm', pd.DataFrame()) if public_fqdn else pd.DataFrame()
        private_fqdn_pc_db_sm = getattr(private_fqdn, 'pc_db_sm', pd.DataFrame()) if private_fqdn else pd.DataFrame()

        severity_pc_mean_fqdn = construct_fqdn.create_pc_db(public_fqdn_pc_db_sm, private_fqdn_pc_db_sm)
        if severity_pc_mean_fqdn is None:
            severity_pc_mean_fqdn = pd.DataFrame()
        if not severity_pc_mean_fqdn.empty:
            severity_pc_mean_fqdn = severity_pc_mean_fqdn.assign(resource_type=1)

        severity_pc_mean = construct.merge_ip_fqdn(severity_pc_mean, severity_pc_mean_fqdn)
        if severity_pc_mean is None:
            severity_pc_mean = pd.DataFrame()

        return severity_pc_mean, pc_history, pc_history_fqdn, pc_rate

    except Exception as e:
        my_logger.error(f"Error in patching_cadence_creation: {e}")
        # Return tuple of 4 empty DataFrames to prevent unpacking errors
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()


@log_decorator("3-7: Preparing tables for mail ...")
def mails_preparation(
        construct: GeneralConstruction,
        public: TableConstruction,
        private: TableConstruction,
        df_total: pd.DataFrame,
        pc_rate: pd.DataFrame
        ) -> None:
    """
    Creates csv sent by email

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    public : class
        The TableConstruction class with public ips
    private : class
        The TableConstruction class with private ips
    df_total : DataFrame
        The dataframe with total rating
    """

    construct.create_data_today_csv(df_total)

    # Defensive check: ensure vulnerable_ips attributes exist
    public_vulnerable_ips = getattr(public, 'vulnerable_ips', pd.DataFrame()) if public else pd.DataFrame()
    private_vulnerable_ips = getattr(private, 'vulnerable_ips', pd.DataFrame()) if private else pd.DataFrame()
    construct.create_vulnerable_ips_csv(public_vulnerable_ips, private_vulnerable_ips)

    construct.create_clean_csv_cor_rate(pc_rate, "on_time_correction_rate")


def create_tables(import_class: DataImport, import_fqdn_class: DataImport):
    """
    Runs all the functions to create rating tables

    Parameters
    ----------
    import_class : class
        The DataImport class

    Returns
    -------
    data_category : DataFrame
        The category rating dataframe
    data_family: DataFrame
        The family rating dataframe
    data_total : DataFrame
        The total rating dataframe
    findings : DataFrame
        The dataframe with all scans for entities since 45 days
    findings_asset : DataFrame
        The dataframe with all scans for assets since 45 days
    patching_cadence : DataFrame
        The dataframe with all scans and information about mean correction delay
    public_class : TableConstruction
        The TableConstruction class with public ips
    private_class : TableConstruction
        The TableConstruction class with private ips
    """

    construct = GeneralConstruction(import_class)
    construct_fqdn = GeneralConstruction(import_fqdn_class)

    public_class, public_fqdn_class = privacy_tables_construction("public", import_class, import_fqdn_class)
    private_class, private_fqdn_class = privacy_tables_construction("private", import_class, import_fqdn_class)

    my_logger.info("Step 3/9: Preparing rating table")
    df_category = data_category_concatenation(construct, construct_fqdn,
                                              public_class, private_class,
                                              public_fqdn_class, private_fqdn_class)
    df_family = data_family_concatenation(construct, construct_fqdn,
                                          public_class, private_class,
                                          public_fqdn_class, private_fqdn_class)
    df_total = data_total_concatenation(construct, construct_fqdn,
                                        public_class, private_class,
                                        public_fqdn_class, private_fqdn_class)

    df_category, df_family, df_total = rating_letters_attribution(
        construct, df_category, df_family, df_total)

    findings, findings_asset, findings_fqdn, findings_fqdn_asset = findings_creation(
        construct, construct_fqdn, public_class, private_class, public_fqdn_class, private_fqdn_class)

    # Defensive unpacking for patching_cadence_creation
    try:
        result = patching_cadence_creation(
            construct, construct_fqdn, public_class, private_class, public_fqdn_class, private_fqdn_class)

        if isinstance(result, tuple) and len(result) == 4:
            severity_pc_mean, pc_history, pc_history_fqdn, pc_rate = result
        else:
            my_logger.error(f"patching_cadence_creation returned unexpected result: {type(result)}, length: {len(result) if hasattr(result, '__len__') else 'N/A'}")
            severity_pc_mean = pd.DataFrame()
            pc_history = pd.DataFrame()
            pc_history_fqdn = pd.DataFrame()
            pc_rate = pd.DataFrame()
    except ValueError as e:
        my_logger.error(f"ValueError in patching_cadence_creation unpacking: {e}")
        severity_pc_mean = pd.DataFrame()
        pc_history = pd.DataFrame()
        pc_history_fqdn = pd.DataFrame()
        pc_rate = pd.DataFrame()

    mails_preparation(construct, public_class, private_class, df_total, pc_rate)
    return [df_category, df_family, df_total, findings,
            findings_asset, findings_fqdn, findings_fqdn_asset,
            pc_history, pc_history_fqdn, severity_pc_mean, public_class,
            private_class, public_fqdn_class, private_fqdn_class]


@log_decorator("Step 4/9: Build & Export AD scoring ...")
def build_ad_rating():
    """
    Build AD rating from data extraction to rating contruction_
    """
    ADrating()


@log_decorator("Step 5/9: Exporting rating tables ...")
def export_rating_tables(
        dc: pd.DataFrame,
        df: pd.DataFrame,
        dt: pd.DataFrame,
        dff: pd.DataFrame,
        dff_fqdn: pd.DataFrame,
        dfa_fqdn: pd.DataFrame,
        dfa: pd.DataFrame,
        pch: pd.DataFrame,
        pch_fqdn: pd.DataFrame,
        pcm: pd.DataFrame) -> None:
    """
    Exports the rating tables with rating_tables_export function from DataExport class

    Parameters
    ----------
    dc : DataFrame
        The category rating dataframe
    df: DataFrame
        The family rating dataframe
    dt : DataFrame
        The total rating dataframe
    dff : DataFrame
        The dataframe with all scans for entities since 45 days about ip
    dfa : DataFrame
        The dataframe with all scans for assets since 45 days about ip
    dff_fqdn :
        The dataframe with all scans for assets since 45 days about fqdn
    dfa_fqdn :
        The dataframe with all scans for assets since 45 days about fqdn
    pch : DataFrame
        The dataframe with all history about of vulnerabilities apparition about ip
    pch_fqdn : DataFrame
        The dataframe with all history about of vulnerabilities apparition about fqdn
    pcm : DataFrame
        The dataframe with mean correction delay by severity and level
    """

    export_class = DataExport()
    export_class.rating_tables_export(dc, df, dt, dff, dff_fqdn, dfa_fqdn, dfa, pch, pch_fqdn, pcm)


@log_decorator("Step 6/9: Sending mail ...")
def send_mail() -> None:
    """
    Sends the mails with rating of the day and vulnerable ips
    """
    send_csv_mail("data_today.csv")
    send_csv_mail("vulnerable_ips.csv")
    send_csv_mail("test")


@log_decorator("7-1: Creating category correction table ...")
def category_correction(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        pub_correction_class: Correction,
        priv_correction_class: Correction,
        pub_correction_fqdn_class: Correction,
        priv_correction_fqdn_class: Correction) -> tuple:
    """
    Creates public and private category correction dataframe and concatenates them

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    pub_correction_class : class
        The Correction class with public ips
    priv_correction_class : class
        The Correction class with private ips

    Returns
    -------
    out : DataFrame
        The dataframe with category correction
    """
    
    try:
        my_logger.info("Starting category_correction function")
        
        my_logger.info("Creating public category correction...")
        cat_cor_pub = pub_correction_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Public category correction created with shape: {cat_cor_pub.shape if hasattr(cat_cor_pub, 'shape') else 'No shape'}")
        
        my_logger.info("Creating private category correction...")
        cat_cor_priv = priv_correction_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Private category correction created with shape: {cat_cor_priv.shape if hasattr(cat_cor_priv, 'shape') else 'No shape'}")
        
        my_logger.info("Creating all category correction...")
        cat_cor_all = pub_correction_class.create_plugin_correction(construct, "category", cat_cor_pub, cat_cor_priv)
        my_logger.info(f"All category correction created with shape: {cat_cor_all.shape if hasattr(cat_cor_all, 'shape') else 'No shape'}")
        
        cat_cor_all = cat_cor_all.assign(privacy="all")
        my_logger.info("Privacy column assigned to all category correction")
        
        my_logger.info("Concatenating public, private and all category corrections...")
        out = pd.concat([cat_cor_pub, cat_cor_priv, cat_cor_all], ignore_index=True)[cat_cor_all.columns]
        my_logger.info(f"Main category correction concatenated with shape: {out.shape}")
        
        my_logger.info("Applying cluster rating corrections...")
        # Defensive check: ensure cluster columns exist
        if "cluster" in out.columns:
            if "cluster_rating_if_corr" in out.columns:
                out["cluster_rating_if_corr"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster_rating_if_corr"])
            out["cluster"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster"])
        else:
            my_logger.warning("cluster column missing, skipping cluster rating corrections")
        my_logger.info("Cluster rating corrections applied")

        my_logger.info("Creating public FQDN category correction...")
        cat_cor_pub_fqdn = pub_correction_fqdn_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Public FQDN category correction created with shape: {cat_cor_pub_fqdn.shape if hasattr(cat_cor_pub_fqdn, 'shape') else 'No shape'}")
        
        my_logger.info("Creating private FQDN category correction...")
        cat_cor_priv_fqdn = priv_correction_fqdn_class.create_privacy_plugin_correction("category")
        my_logger.info(f"Private FQDN category correction created with shape: {cat_cor_priv_fqdn.shape if hasattr(cat_cor_priv_fqdn, 'shape') else 'No shape'}")
        
        my_logger.info("Creating all FQDN category correction...")
        cat_cor_all_fqdn = pub_correction_class.create_plugin_correction(construct_fqdn, "category", cat_cor_pub_fqdn, cat_cor_priv_fqdn)
        my_logger.info(f"All FQDN category correction created with shape: {cat_cor_all_fqdn.shape if hasattr(cat_cor_all_fqdn, 'shape') else 'No shape'}")
        
        cat_cor_all_fqdn = cat_cor_all_fqdn.assign(privacy="all")
        my_logger.info("Privacy column assigned to all FQDN category correction")
        
        my_logger.info("Concatenating FQDN category corrections...")
        out_fqdn = pd.concat([cat_cor_pub_fqdn, cat_cor_priv_fqdn, cat_cor_all_fqdn], ignore_index=True)[cat_cor_all.columns]
        my_logger.info(f"FQDN category correction concatenated with shape: {out_fqdn.shape}")
        
        my_logger.info("Applying FQDN cluster rating corrections...")
        # Defensive check: ensure cluster columns exist
        if "cluster" in out_fqdn.columns:
            if "cluster_rating_if_corr" in out_fqdn.columns:
                out_fqdn["cluster_rating_if_corr"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster_rating_if_corr"])
            out_fqdn["cluster"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster"])
        else:
            my_logger.warning("cluster column missing in FQDN DataFrame, skipping cluster rating corrections")
        my_logger.info("FQDN cluster rating corrections applied")

        my_logger.info("Category correction function completed successfully")
        return out, out_fqdn
        
    except Exception as e:
        my_logger.error(f"Error in category_correction: {str(e)}")
        my_logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        my_logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty DataFrames with expected structure instead of None
        empty_df = pd.DataFrame()
        return empty_df, empty_df


@log_decorator("7-2: Creating family correction table ...")
def family_correction(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        pub_correction_class: Correction,
        priv_correction_class: Correction,
        pub_correction_fqdn_class: Correction,
        priv_correction_fqdn_class: Correction) -> tuple:
    """
    Creates public and private family correction dataframe and concatenates them
    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    pub_correction_class : class
        The Correction class with public ips
    priv_correction_class : class
        The Correction class with private ips

    Returns
    -------
    out : DataFrame
        The dataframe with family correction
    """
    
    try:
        fam_cor_pub = pub_correction_class.create_privacy_plugin_correction("family")
        fam_cor_priv = priv_correction_class.create_privacy_plugin_correction("family")
        fam_cor_all = pub_correction_class.create_plugin_correction(construct, "family", fam_cor_pub, fam_cor_priv)
        fam_cor_all = fam_cor_all.assign(privacy="all")
        out = pd.concat([fam_cor_pub, fam_cor_priv, fam_cor_all], ignore_index=True)[fam_cor_all.columns]
        # Defensive check: ensure cluster columns exist
        if "cluster" in out.columns:
            if "cluster_rating_if_corr" in out.columns:
                out["cluster_rating_if_corr"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster_rating_if_corr"])
            out["cluster"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster"])
        else:
            my_logger.warning("cluster column missing in family correction, skipping cluster rating corrections")

        fam_cor_pub_fqdn = pub_correction_fqdn_class.create_privacy_plugin_correction("family")
        fam_cor_priv_fqdn = priv_correction_fqdn_class.create_privacy_plugin_correction("family")
        fam_cor_all_fqdn = pub_correction_fqdn_class.create_plugin_correction(construct_fqdn, "family", fam_cor_pub_fqdn, fam_cor_priv_fqdn)
        fam_cor_all_fqdn = fam_cor_all_fqdn.assign(privacy="all")
        out_fqdn = pd.concat([fam_cor_pub_fqdn, fam_cor_priv_fqdn, fam_cor_all_fqdn], ignore_index=True)[fam_cor_all_fqdn.columns]
        # Defensive check: ensure cluster columns exist
        if "cluster" in out_fqdn.columns:
            if "cluster_rating_if_corr" in out_fqdn.columns:
                out_fqdn["cluster_rating_if_corr"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster_rating_if_corr"])
            out_fqdn["cluster"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster"])
        else:
            my_logger.warning("cluster column missing in FQDN family correction, skipping cluster rating corrections")
        
        return out, out_fqdn
        
    except Exception as e:
        my_logger.error(f"Error in family_correction: {str(e)}")
        empty_df = pd.DataFrame()
        return empty_df, empty_df


@log_decorator("7-3: Creating ip correction table ...")
def ip_correction(
        construct: GeneralConstruction,
        construct_fqdn: GeneralConstruction,
        pub_correction_class: Correction,
        priv_correction_class: Correction,
        pub_correction_fqdn_class: Correction,
        priv_correction_fqdn_class: Correction) -> pd.DataFrame:
    """
    Creates public and private ip correction dataframe and concatenates them

    Parameters
    ----------
    construct : class
        The GeneralConstruction class
    pub_correction_class : class
        The Correction class with public ips
    priv_correction_class : class
        The Correction class with private ips

    Returns
    -------
    out : DataFrame
        The dataframe with ip correction
    """

    pub = pub_correction_class.create_ip_correction()
    priv = priv_correction_class.create_ip_correction()
    all = pub_correction_class.create_plugin_correction(construct, "category", pub, priv, rt_correction=True)
    all = all.assign(privacy="all")
    out = pd.concat([pub, priv, all], ignore_index=True)[all.columns]
    # Defensive check: ensure cluster columns exist
    if "cluster" in out.columns:
        if "cat_cluster_rating_if_corr" in out.columns:
            out["cat_cluster_rating_if_corr"] = np.where(out["cluster"] >= 1000, np.nan, out["cat_cluster_rating_if_corr"])
        out["cluster"] = np.where(out["cluster"] >= 1000, np.nan, out["cluster"])

    pub_fqdn = pub_correction_fqdn_class.create_ip_correction()
    priv_fqdn = priv_correction_fqdn_class.create_ip_correction()

    all_fqdn = pub_correction_fqdn_class.create_plugin_correction(construct_fqdn, "category", pub_fqdn, priv_fqdn, rt_correction=True)
    all_fqdn = all_fqdn.assign(privacy="all")
    out_fqdn = pd.concat([pub_fqdn, priv_fqdn, all_fqdn], ignore_index=True)[all_fqdn.columns]
    # Defensive column renaming
    if "ip_id" in out_fqdn.columns:
        out_fqdn.rename(columns={"ip_id": "fqdn_id"}, inplace=True)

    # Defensive check: ensure cluster columns exist
    if "cluster" in out_fqdn.columns:
        if "cat_cluster_rating_if_corr" in out_fqdn.columns:
            out_fqdn["cat_cluster_rating_if_corr"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cat_cluster_rating_if_corr"])
        out_fqdn["cluster"] = np.where(out_fqdn["cluster"] >= 1000, np.nan, out_fqdn["cluster"])

    return out, out_fqdn


def create_correction_table(
        import_class: DataImport,
        import_fqdn_class: DataImport,
        public_class: TableConstruction,
        private_class: TableConstruction,
        public_fqdn_class: TableConstruction,
        private_fqdn_class: TableConstruction):
    """
    Runs all the functions to create the correction dataframes

    Parameters
    ----------
    import_class : class
        The DataImport class
    import_fqdn_class : class
        The DataImport fqdn class
    public_class : class
        The TableConstruction class with public ips
    private_class : class
        The TableConstruction class with private ips
    public_class : class
        The TableConstruction class with public fqdn
    private_class : class
        The TableConstruction class with private fqdn

    Returns
    -------
    data_category_correction : DataFrame
        The dataframe with category correction
    data_family_correction : DataFrame
        The dataframe with family correction
    data_ip_correction : DataFrame
        The dataframe with ip correction
    data_fqdn_correction : DataFrame
        The dataframe with fqdn correction
    """

    my_logger.info("Step 7/9: Creating correction tables")
    
    try:
        my_logger.info("Creating Correction class instances...")
        pub_correction_class = Correction(public_class)
        priv_correction_class = Correction(private_class)
        pub_correction_fqdn_class = Correction(public_fqdn_class)
        priv_correction_fqdn_class = Correction(private_fqdn_class)
        my_logger.info("Correction class instances created successfully")
        
        my_logger.info("Creating GeneralConstruction class instances...")
        construct = GeneralConstruction(import_class)
        construct_fqdn = GeneralConstruction(import_fqdn_class)
        my_logger.info("GeneralConstruction class instances created successfully")

        # Validate correction table creation
        my_logger.info("Starting category correction...")
        try:
            result = category_correction(
                construct, construct_fqdn, pub_correction_class, priv_correction_class,
                pub_correction_fqdn_class, priv_correction_fqdn_class)

            # Defensive unpacking: handle cases where result might not be a tuple of 2 elements
            if isinstance(result, tuple) and len(result) == 2:
                data_category_correction, data_category_correction_fqdn = result
            else:
                my_logger.error(f"Category correction returned unexpected result: {type(result)}, length: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                data_category_correction = pd.DataFrame()
                data_category_correction_fqdn = pd.DataFrame()
        except ValueError as e:
            my_logger.error(f"ValueError in category correction unpacking: {e}")
            data_category_correction = pd.DataFrame()
            data_category_correction_fqdn = pd.DataFrame()
        my_logger.info("Category correction completed")

        if data_category_correction is None or data_category_correction_fqdn is None:
            my_logger.error("Category correction returned None - using empty DataFrames")
            data_category_correction = pd.DataFrame()
            data_category_correction_fqdn = pd.DataFrame()

        my_logger.info("Starting family correction...")
        try:
            result = family_correction(
                construct, construct_fqdn, pub_correction_class, priv_correction_class,
                pub_correction_fqdn_class, priv_correction_fqdn_class)

            # Defensive unpacking: handle cases where result might not be a tuple of 2 elements
            if isinstance(result, tuple) and len(result) == 2:
                data_family_correction, data_family_correction_fqdn = result
            else:
                my_logger.error(f"Family correction returned unexpected result: {type(result)}, length: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                data_family_correction = pd.DataFrame()
                data_family_correction_fqdn = pd.DataFrame()
        except ValueError as e:
            my_logger.error(f"ValueError in family correction unpacking: {e}")
            data_family_correction = pd.DataFrame()
            data_family_correction_fqdn = pd.DataFrame()
        my_logger.info("Family correction completed")

        if data_family_correction is None or data_family_correction_fqdn is None:
            my_logger.error("Family correction returned None - using empty DataFrames")
            data_family_correction = pd.DataFrame()
            data_family_correction_fqdn = pd.DataFrame()

        my_logger.info("Starting IP correction...")
        try:
            result = ip_correction(
                construct, construct_fqdn, pub_correction_class, priv_correction_class,
                pub_correction_fqdn_class, priv_correction_fqdn_class)

            # Defensive unpacking: handle cases where result might not be a tuple of 2 elements
            if isinstance(result, tuple) and len(result) == 2:
                data_ip_correction, data_ip_correction_fqdn = result
            else:
                my_logger.error(f"IP correction returned unexpected result: {type(result)}, length: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                data_ip_correction = pd.DataFrame()
                data_ip_correction_fqdn = pd.DataFrame()
        except ValueError as e:
            my_logger.error(f"ValueError in IP correction unpacking: {e}")
            data_ip_correction = pd.DataFrame()
            data_ip_correction_fqdn = pd.DataFrame()
        my_logger.info("IP correction completed")

        if data_ip_correction is None or data_ip_correction_fqdn is None:
            my_logger.error("IP correction returned None - using empty DataFrames")
            data_ip_correction = pd.DataFrame()
            data_ip_correction_fqdn = pd.DataFrame()

        # Safe assignment with empty DataFrame handling
        my_logger.info("Processing category correction results...")
        if not data_category_correction.empty:
            data_category_correction = data_category_correction.assign(resource_type=0)
            my_logger.info("Resource type 0 assigned to category correction")
        if not data_category_correction_fqdn.empty:
            data_category_correction_fqdn = data_category_correction_fqdn.assign(resource_type=1)
            my_logger.info("Resource type 1 assigned to FQDN category correction")
            data_category_correction = construct.merge_ip_fqdn(data_category_correction, data_category_correction_fqdn)
            my_logger.info("Category corrections merged")
        
        my_logger.info("Processing family correction results...")
        if not data_family_correction.empty:
            data_family_correction = data_family_correction.assign(resource_type=0)
            my_logger.info("Resource type 0 assigned to family correction")
        if not data_family_correction_fqdn.empty:
            data_family_correction_fqdn = data_family_correction_fqdn.assign(resource_type=1)
            my_logger.info("Resource type 1 assigned to FQDN family correction")
            data_family_correction = construct.merge_ip_fqdn(data_family_correction, data_family_correction_fqdn)
            my_logger.info("Family corrections merged")

        my_logger.info("Correction table creation completed successfully")
        return data_category_correction, data_family_correction, data_ip_correction, data_ip_correction_fqdn
        
    except Exception as e:
        my_logger.error(f"Critical error in create_correction_table: {str(e)}")
        my_logger.error(f"Exception type: {type(e).__name__}")
        import traceback
        my_logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty DataFrames to prevent unpacking errors
        empty_df = pd.DataFrame()
        return empty_df, empty_df, empty_df, empty_df


@log_decorator("Step 8/9: Exporting correction tables ...")
def export_correction_table(category: pd.DataFrame, family: pd.DataFrame,
                            ip: pd.DataFrame, fqdn: pd.DataFrame) -> None:
    """
    Exports the correction tables with correction_tables_export function from DataExport class

    Parameters
    ----------
    category : DataFrame
        The dataframe with category correction
    family: DataFrame
        The dataframe with family correction
    ip : DataFrame
        The dataframe with ip correction
    fqdn : DataFrame
        The dataframe with fqdn correction
    """

    export_class = DataExport()
    export_class.correction_tables_export(category, family, ip, fqdn)


@log_decorator("Step 9/9: Checking the day's data  ...")
def check_data() -> None:
    """
    Check that the day's data is written to the tables
    """
    export_class = DataExport()
    export_class.table_write_test()


# # TODO: remove after update schema
def useless_col_schema(category, family, total):
    # Defensive check: handle None or empty DataFrames
    if category is None or category.empty:
        category = pd.DataFrame(columns=['findings_counter'])
    else:
        category = category.assign(findings_counter=0)

    if family is None or family.empty:
        family = pd.DataFrame(columns=['findings_counter'])
    else:
        family = family.assign(findings_counter=0)

    if total is None or total.empty:
        total = pd.DataFrame(columns=['findings_counter'])
    else:
        total = total.assign(findings_counter=0)

    return category, family, total


# # TODO: remove after update schema
def useless_col_schema2(cat_cor, fam_cor, ip_cor, ip_cor_fqdn):
    # Defensive check: handle None or empty DataFrames
    if fam_cor is None or fam_cor.empty:
        fam_cor = pd.DataFrame(columns=['findings_counter', 'weight'])
    else:
        fam_cor = fam_cor.assign(findings_counter=0)
        fam_cor = fam_cor.assign(weight=0)

    if cat_cor is None or cat_cor.empty:
        cat_cor = pd.DataFrame(columns=['findings_counter', 'weight'])
    else:
        cat_cor = cat_cor.assign(findings_counter=0)
        cat_cor = cat_cor.assign(weight=0)

    if ip_cor is None or ip_cor.empty:
        ip_cor = pd.DataFrame(columns=['findings_counter'])
    else:
        ip_cor = ip_cor.assign(findings_counter=0)

    if ip_cor_fqdn is None or ip_cor_fqdn.empty:
        ip_cor_fqdn = pd.DataFrame(columns=['findings_counter'])
    else:
        ip_cor_fqdn = ip_cor_fqdn.assign(findings_counter=0)

    return cat_cor, fam_cor, ip_cor, ip_cor_fqdn


def main() -> None:
    """
    Runs all functions to run the algorithm
    """
    try:
        config_logger()
        logging.basicConfig(level=logging.INFO)
        my_logger.info("Lancement du moteur de notation IA (" + get_version_ref()[0] + " - " + get_version_ref()[1] + ")")

        # Get all source data from the database
        try:
            import_class, import_fqdn_class = import_data()
        except Exception as e:
            my_logger.error(f"Error importing data: {e}")
            return

        # Check if our source data is structured (column check) and dataframes are populated
        try:
            check_sources(import_class, my_logger, prefix="[IP] ")
            check_sources(import_fqdn_class, my_logger, prefix="[FQDN] ")
        except Exception as e:
            my_logger.error(f"Error checking sources: {e}")
            # Continue execution even if source check fails

        # Build and export all data
        try:
            dc, df, dt, dff, dfa, dff_fqdn, dfa_fqdn, pc_h, pch_fqdn, pc_m, public_class, private_class, public_fqdn_class, private_fqdn_class = create_tables(import_class, import_fqdn_class)
            dc, df, dt = useless_col_schema(dc, df, dt)
        except Exception as e:
            my_logger.error(f"Error creating tables: {e}")
            # Create empty DataFrames to prevent downstream errors
            dc = df = dt = dff = dfa = dff_fqdn = dfa_fqdn = pc_h = pch_fqdn = pc_m = pd.DataFrame()
            public_class = private_class = public_fqdn_class = private_fqdn_class = None

        try:
            build_ad_rating()
        except Exception as e:
            my_logger.error(f"Error building AD rating: {e}")
            # Continue execution

        try:
            export_rating_tables(dc, df, dt, dff, dfa, dff_fqdn, dfa_fqdn, pc_h, pch_fqdn, pc_m)
        except Exception as e:
            my_logger.error(f"Error exporting rating tables: {e}")
            # Continue execution

        try:
            send_mail()
        except Exception as e:
            my_logger.error(f"Error sending mail: {e}")
            # Continue execution

        # Build and export correction data
        try:
            cat_cor, fam_cor, ip_cor, ip_cor_fqdn = create_correction_table(
                import_class, import_fqdn_class,
                public_class, private_class,
                public_fqdn_class, private_fqdn_class)
            cat_cor, fam_cor, ip_cor, ip_cor_fqdn = useless_col_schema2(cat_cor, fam_cor, ip_cor, ip_cor_fqdn)
        except Exception as e:
            my_logger.error(f"Error creating correction tables: {e}")
            # Create empty DataFrames to prevent downstream errors
            cat_cor = fam_cor = ip_cor = ip_cor_fqdn = pd.DataFrame()

        try:
            export_correction_table(cat_cor, fam_cor, ip_cor, ip_cor_fqdn)
        except Exception as e:
            my_logger.error(f"Error exporting correction tables: {e}")
            # Continue execution

        # Check and inform on which tables have been updated to the database
        try:
            check_data()
        except Exception as e:
            my_logger.error(f"Error checking data: {e}")
            # Continue execution

        my_logger.info("Cyber rating pipeline completed successfully")

    except Exception as e:
        my_logger.error(f"Critical error in main function: {e}")
        import traceback
        my_logger.error(f"Full traceback: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
